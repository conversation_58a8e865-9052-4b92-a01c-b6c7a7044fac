-- loan_requests table
UPSERT
INTO loan_requests (id,actor_id,offer_id, loan_program, orch_id, loan_account_id, vendor_request_id, vendor, type, status, sub_status,details,created_at,updated_at,deleted_at,completed_at, client_request_id) VALUES
	('request-id-2','act-2','off-2', 'LOAN_PROGRAM_PRE_APPROVED_LOAN', 'orch-2','*********','req-2','FEDERAL','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CREATED','LOAN_REQUEST_SUB_STATUS_CREATED','{}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,NULL, 'cr-id-1'),
	('request-id-6','act-6','off-6', 'LOAN_PROGRAM_PRE_APPROVED_LOAN', 'orch-6','acc-6','req-6','FEDERAL','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CREATED','LOAN_REQUEST_SUB_STATUS_CREATED','{"loanInfo": {"amount": {"currencyCode": "INR", "units": "100000"}, "deductions": {"advanceInterest": {"currencyCode": "INR", "units": "500"}, "gst": {"currencyCode": "INR", "units": "200"}, "processingFee": {"currencyCode": "INR", "units": "300"}, "totalDeductions": {"currencyCode": "INR", "units": "1000"}}, "disbursalAmount": {"currencyCode": "INR", "units": "99000"}, "emiAmount": {"currencyCode": "INR", "units": "8000"}, "interestRate": 14.5, "tenureInMonths": 12, "totalPayable": {"currencyCode": "INR", "units": "101000"}}, "otpInfo": {"attemptsCount": 0, "maxAttempts": 3, "otp": "123456"}, "phoneNumber": {"countryCode": 91, "nationalNumber": "9876543210"}}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,NULL, 'cr-id-2'),
	('request-id-7','act-7','off-7', 'LOAN_PROGRAM_PRE_APPROVED_LOAN', 'orch-7','acc-7','req-7','LIQUILOANS','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CANCELLED','LOAN_REQUEST_SUB_STATUS_CREATED','{}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,'2062-04-01 11:48:50.662941+05:30', 'cr-id-3'); -- Use very large completed_at timestamp so that it is returned in TC

UPSERT
INTO loan_accounts (id,actor_id,loan_account_id, loan_type, ifsc_code, loan_amount, disbursed_amount, outstanding_amount, total_payable_amount, loan_end_date, maturity_date, vendor, details, status, created_at,updated_at,deleted_at,loan_program) VALUES
	('account-id-2','act-2','acc-2','LOAN_TYPE_PERSONAL','ifsc-2','{"currency_code": "INR", "units": 5000}','{"currency_code": "INR", "units": 4000}','{"currency_code": "INR", "units": 3000}','{"currency_code": "INR", "units": 3000}','2022-08-15','2022-08-10','LIQUILOANS','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
    ('account-id-ll-2','act-ll-2','acc-ll-2','LOAN_TYPE_PERSONAL','ifsc-ll-2','{"currency_code": "INR", "units": 14000}','{"currency_code": "INR", "units": 12500}','{"currency_code": "INR", "units": 18000}','{"currency_code": "INR", "units": 18000}','2022-08-15','2022-08-10','LIQUILOANS','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30', NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
	('account-id-ll-3','act-ll-3','acc-ll-3','LOAN_TYPE_PERSONAL','ifsc-ll-3','{"currency_code": "INR", "units": 14000}','{"currency_code": "INR", "units": 12500}','{"currency_code": "INR", "units": 18000}','{"currency_code": "INR", "units": 18000}','2022-08-15','2022-08-10','LIQUILOANS','{}','LOAN_ACCOUNT_STATUS_ACTIVE','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30', NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
	('account-id-4','act-4','acc-4','LOAN_TYPE_EARLY_SALARY','ifsc-4','{"currency_code": "INR", "units": 5000}','{"currency_code": "INR", "units": 4000}','{"currency_code": "INR", "units": 3000}','{"currency_code": "INR", "units": 3000}','2022-08-15','2022-08-10','LIQUILOANS','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN');

UPSERT
INTO loan_installment_info (id, account_id, total_amount, start_date, end_date, total_installment_count, next_installment_date, details, status, deactivated_at, created_at, updated_at, deleted_at) VALUES
	('inst-info-id-2', 'acc-2', '{"currency_code": "INR", "units": 50000}', '2022-08-15', '2022-10-15', 10, '2022-09-15', '{}', 'LOAN_INSTALLMENT_INFO_STATUS_ACTIVE', NULL, '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT
INTO loan_step_executions (id, actor_id, ref_id, orch_id, flow, step_name, details, status, sub_status, staled_at, completed_at, created_at, updated_at, deleted_at) VALUES
	('step-exec-id-2', 'act-2', 'ref-2', 'orch-2', 'LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION', 'LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED', '{}', 'LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED', 'LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED',NULL, NULL, '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL),
	('step-exec-id-6', 'act-6', 'ref-6', 'orch-6', 'LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION', 'LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK', '{}', 'LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED', 'LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED',NULL, '2022-04-01 11:48:50.662941+05:30', '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL);

UPSERT
INTO loan_activities (id, loan_account_id, details, type, created_at, updated_at, deleted_at, reference_id) VALUES
	('act-id-2', 'acc-2', '{}', 'LOAN_ACTIVITY_TYPE_EMI','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL, 'ref-id-1'),
	('act-id-3', 'acc-2', '{}', 'LOAN_ACTIVITY_TYPE_EMI','2022-05-01 11:48:50.662941+05:30','2022-05-01 11:48:50.662941+05:30',NULL, 'ref-id-3');

UPSERT
INTO loan_payment_requests (id, actor_id, account_id, amount, type, orch_id, details, status, sub_status, created_at, updated_at, deleted_at, parent_id) VALUES
	('id-2', 'act-2', 'acc-2', '{"currency_code": "INR", "units": 50000}', 'LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM', 'orch-2', '{}', 'LOAN_PAYMENT_REQUEST_STATUS_CREATED', 'LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL, 'parent-id-1');

-- loan_offers table
UPSERT
INTO loan_offers (id, actor_id, vendor_offer_id, loan_program, vendor, offer_constraints, processing_info, valid_since, valid_till, deactivated_at, created_at, updated_at, loan_offer_eligibility_criteria_id) VALUES
('loan-offer-id-1', 'actor-1', 'vendor-offer-id-1', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','FEDERAL', '{}', '{}', '2022-07-25 00:00:00.000000 +05:30', '2028-07-25 00:00:00.000000 +05:30', NULL, '2022-07-25 00:00:00.000000 +05:30', '2022-07-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-1'),
('loan-offer-id-2', 'actor-1', 'vendor-offer-id-3', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','FEDERAL', '{}', '{}', '2022-08-25 00:00:00.000000 +05:30', '2028-08-25 00:00:00.000000 +05:30', '2023-08-26 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-1'),
('loan-offer-id-3', 'actor-3', 'vendor-offer-id-4', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','LIQUILOANS', '{}', '{}', '2022-08-25 00:00:00.000000 +05:30', '2028-08-25 00:00:00.000000 +05:30', NULL, '2022-08-25 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-4'),
('loan-offer-id-5', 'actor-5', 'vendor-offer-id-5', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','LIQUILOANS', '{}', '{}', '2022-08-25 00:00:00.000000 +05:30', '2028-08-25 00:00:00.000000 +05:30', NULL, '2022-08-25 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-4'),
('loan-offer-id-6', 'actor-5', 'vendor-offer-id-6', 'LOAN_PROGRAM_FLDG','LIQUILOANS', '{}', '{}', '2022-08-25 00:00:00.000000 +05:30', '2028-08-25 00:00:00.000000 +05:30', NULL, '2022-08-25 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-4');

-- loan_offers table
UPSERT
INTO loan_offer_eligibility_criteria (id, actor_id, vendor, status, sub_status, loan_scheme, vendor_response, created_at, updated_at, batch_id, loan_program) VALUES
	('loan-offer-eligibility-id-1', 'actor-1', 'FEDERAL', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED', '', '{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30', 'BI-09-03-2023', 'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
	('loan-offer-eligibility-id-2', 'actor-2', 'FEDERAL', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED', '','{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30', 'BI-09-03-2023', 'LOAN_PROGRAM_PRE_APPROVED_LOAN');

-- loan_installment_payout table
UPSERT
INTO loan_installment_payout (id, loan_installment_info_id, amount, due_date, payout_date, details, status, loan_account_id) VALUES
    ('loan-installment-payout-id-1', 'inst-info-id-2', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', 'loan-account-id-1');

UPSERT
INTO loan_installment_payout (id, loan_installment_info_id, amount, due_date, payout_date, details, status, vendor_installment_id, due_amount, loan_account_id) VALUES
    ('loan-installment-payout-id-3', 'loan-installment-info-id-common', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', '1', '{"currency_code": "INR", "units": 100000}', 'loan-account-id-3'),
	('loan-installment-payout-id-4', 'loan-installment-info-id-common', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', '2', '{"currency_code": "INR", "units": 100000}', 'loan-account-id-4'),
	('loan-installment-payout-id-5', 'loan-installment-info-id-common', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', '3', '{"currency_code": "INR", "units": 100000}', 'loan-account-id-5');


-- loan_applicants table
UPSERT
INTO loan_applicants (id, actor_id, vendor, vendor_applicant_id, vendor_request_id, loan_program, status, sub_status, created_at, updated_at, deleted_at) VALUES ('loan-applicant-id-1', 'actor-id-1', 'LIQUILOANS', 'vendor-applicant-id-1', 'vendor-request-id-1', 'LOAN_PROGRAM_PRE_APPROVED_LOAN', 'LOAN_APPLICANT_STATUS_APPROVED', 'LOAN_APPLICANT_SUB_STATUS_DOCUMENT_UPLOADED', '2023-02-14 10:52:06.373584 +00:00', '2023-02-14 10:52:06.373584 +00:00', NULL);
