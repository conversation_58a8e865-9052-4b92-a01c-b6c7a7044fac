ALTER TABLE loan_installment_payout ADD COLUMN IF NOT EXISTS vendor_installment_id STRING DEFAULT NULL;
ALTER TABLE loan_installment_payout ADD COLUMN IF NOT EXISTS principal_amount JSONB DEFAULT NULL;
ALTER TABLE loan_installment_payout ADD COLUMN IF NOT EXISTS interest_amount JSONB DEFAULT NULL;

COMMENT ON COLUMN loan_installment_payout.vendor_installment_id IS 'installment id assigned by the vendor';
COMMENT ON COLUMN loan_installment_payout.principal_amount IS 'principal amount of a given installment';
COMMENT ON COLUMN loan_installment_payout.interest_amount IS 'interest amount of a given installment';
