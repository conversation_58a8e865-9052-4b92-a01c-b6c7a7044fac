ALTER TABLE IF EXISTS loan_offers ADD COLUMN IF NOT EXISTS loan_program STRING NOT NULL DEFAULT 'LOAN_PROGRAM_PRE_APPROVED_LOAN';
COMMENT
	ON COLUMN loan_offers.loan_program IS 'loan program is the type of loan applied by user, eg: pre-approved loan, early salary by default setting as pre-approved-loan';

ALTER TABLE IF EXISTS loan_requests ADD COLUMN IF NOT EXISTS loan_program STRING NOT NULL DEFAULT 'LOAN_PROGRAM_PRE_APPROVED_LOAN';
COMMENT
	ON COLUMN loan_requests.loan_program IS 'loan program is the type of loan applied by user, eg: pre-approved loan, early salary';
