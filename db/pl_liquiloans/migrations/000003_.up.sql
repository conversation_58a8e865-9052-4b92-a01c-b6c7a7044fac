CREATE TABLE if not exists workflow_requests
(
	id            STRING      NOT NULL,
	actor_id      STRING      NULL,
	stage         STRING      NOT NULL,
	status        STRING      NOT NULL,
	version       STRING      NOT NULL,
	type          STRING      NOT NULL,
	payload       BYTES       NULL,
	client_req_id STRING      NOT NULL,
	ownership     STRING      NOT NULL,
	created_at    TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at    TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at    TIMESTAMPTZ NULL,
	next_action   JSONB       NULL,
	CONSTRAINT workflow_requests_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX workflow_requests_client_req_id_key (client_req_id ASC),
	INDEX workflow_requests_updated_at_idx (updated_at ASC),
	INDEX workflow_requests_type_stage_status_created_at_idx (type ASC, stage ASC, status ASC, created_at ASC),
	FAMILY frequently_updated (stage, status, updated_at, next_action),
	FAMILY "primary" (id, actor_id, version, type, payload, client_req_id, ownership, created_at, deleted_at)
);
COMMENT ON COLUMN workflow_requests.payload IS e'An opaque blob containing the data needed for processing activity for a workflow.\n     This might vary based on the type of workflow. The data inside the blob will depend on underlying domain service';
COMMENT ON COLUMN workflow_requests.client_req_id IS e'Client details corresponding to the service initiating workflow request.\n     The combination of client and client_req_id must be unique';


CREATE TABLE if not exists workflow_histories
(
	wf_req_id           STRING      NOT NULL,
	stage               STRING      NOT NULL,
	status              STRING      NOT NULL,
	created_at          TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at          TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at          TIMESTAMPTZ NULL,
	completed_at        TIMESTAMPTZ NULL,
	attempts            INT8        NOT NULL DEFAULT 1:::INT8,
	failure_description STRING      NULL,
	rowid               INT8        NOT VISIBLE NOT NULL DEFAULT unique_rowid(),
	payload             BYTES       NULL,
	ext_req_id          STRING      NULL,
	id                  UUID        NULL     DEFAULT gen_random_uuid(),
	CONSTRAINT workflow_histories_pkey PRIMARY KEY (rowid ASC),
	INDEX workflow_histories_updated_at_idx (updated_at ASC),
	INDEX workflow_histories_wf_req_id_stage_idx (wf_req_id ASC, stage ASC),
	FAMILY frequently_updated (stage, status, updated_at, attempts, rowid, payload, ext_req_id),
	FAMILY "primary" (wf_req_id, created_at, deleted_at, completed_at, failure_description, id)
);
COMMENT ON COLUMN workflow_histories.wf_req_id IS 'foreign key from workflows req table';
COMMENT ON COLUMN workflow_histories.stage IS 'Current stage of the workflow';
COMMENT ON COLUMN workflow_histories.status IS 'Latest status of the stage';
COMMENT ON COLUMN workflow_histories.attempts IS 'Number of attempts made to process the order stage. This can be used by the orchestration engine to handle poison pill request during batch processing, etc.';
COMMENT ON COLUMN workflow_histories.failure_description IS 'Optional: Failure description as returned from the activity. The same can be used to display message on the UI if needed. ';
COMMENT ON COLUMN workflow_histories.payload IS 'payload is a json field used to store stage specific payload';
COMMENT ON COLUMN workflow_histories.ext_req_id IS 'If a stage is required to raise request with other services, ext_req_id will store the Identifier received. It can be mainly used for reverse lookups';
