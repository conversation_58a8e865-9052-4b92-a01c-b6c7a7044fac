-- 1. <PERSON><PERSON> Offers
CREATE TABLE IF NOT EXISTS loan_offers
(
	id                                 STRING      NOT NULL,
	actor_id                           STRING      NOT NULL,
	vendor_offer_id                    STRING      NOT NULL,
	vendor                             STRING      NOT NULL,
	offer_constraints                  JSONB       NOT NULL,
	processing_info                    JSONB       NOT NULL,
	valid_since                        TIMESTAMPTZ NOT NULL,
	valid_till                         TIMESTAMPTZ NOT NULL,
	deactivated_at                     TIMESTAMPTZ NULL,
	created_at                         TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at                         TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at                         TIMESTAMPTZ NULL,
	loan_offer_eligibility_criteria_id STRING      NULL,

	PRIMARY KEY (id ASC),
	UNIQUE INDEX loan_offers_unique_idx_vendor_offer_id_vendor (vendor_offer_id ASC, vendor ASC),
	INDEX loan_offers_idx_updated_at (updated_at DESC),
	INDEX loan_offers_idx_actor_id (actor_id DESC),
	FAMILY "frequently_updated" (updated_at, processing_info, offer_constraints),
	FAMILY "seldom_updated" (vendor_offer_id, deactivated_at, valid_since, valid_till)
);

COMMENT ON TABLE loan_offers IS 'table to store all the loan offers received from vendor for an actor';
COMMENT ON COLUMN loan_offers.actor_id IS 'actor for which loan offer is available';
COMMENT ON COLUMN loan_offers.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"vendor who has offered loan"}';
COMMENT ON COLUMN loan_offers.vendor_offer_id IS 'id for the offer provided by vendor';
COMMENT ON COLUMN loan_offers.offer_constraints IS '{"proto_type":"preapprovedloanPb.OfferConstraints", "comment":"loan offer constraints like max loan amount, max EMI amount, max loan tenure"}';
COMMENT ON COLUMN loan_offers.processing_info IS '{"proto_type":"preapprovedloanPb.ProcessingInfo", "comment":"loan offer processing info like interest rate, processing fee"}';
COMMENT ON COLUMN loan_offers.valid_since IS 'loan offer validity start time';
COMMENT ON COLUMN loan_offers.valid_till IS 'loan offer validity end time';
COMMENT ON COLUMN loan_offers.created_at IS 'loan offer creation time';
COMMENT ON COLUMN loan_offers.updated_at IS 'loan offer latest update time';
COMMENT ON COLUMN loan_offers.deactivated_at IS 'loan offer deactivation time';
COMMENT ON COLUMN loan_offers.loan_offer_eligibility_criteria_id IS 'loan_offer_eligibility_criteria_id maps a loan offer from vendor with the loan_offer_eligibility_criteria table, can be used to check in which cycle this loan offer got created.';

-- 2. Loan Offer Eligibility Criteria
CREATE TABLE IF NOT EXISTS loan_offer_eligibility_criteria
(
	id              STRING      NOT NULL,
	actor_id        STRING      NOT NULL,
	vendor          STRING      NOT NULL,
	status          STRING      NOT NULL,
	vendor_response JSONB       NULL,
	created_at      TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at      TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at      TIMESTAMPTZ NULL,
	offer_id        STRING      NULL REFERENCES loan_offers (id) ON DELETE SET NULL,

	PRIMARY KEY (id ASC),
	INDEX loan_offer_eligibility_criteria_actor_id_idx (actor_id),
	INDEX loan_offer_eligibility_criteria_updated_at_idx (updated_at DESC)
);

COMMENT ON TABLE loan_offer_eligibility_criteria IS 'table to store all the actors who are eligible for loan from BA side';
COMMENT ON COLUMN loan_offer_eligibility_criteria.actor_id IS 'actor who is eligible for loan from BA';
COMMENT ON COLUMN loan_offer_eligibility_criteria.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"vendor to whom loan offer is requested"}';
COMMENT ON COLUMN loan_offer_eligibility_criteria.status IS '{"proto_type":"preapprovedloan.LoanOfferEligibilityCriteriaStatus", "comment":"status of the eligibility"}';
COMMENT ON COLUMN loan_offer_eligibility_criteria.vendor_response IS '{"proto_type":"preapprovedloan.VendorResponse", "comment":"response timestamp from vendor"}';

-- 3. Loan Accounts
CREATE TABLE IF NOT EXISTS loan_accounts
(
	id                   STRING      NOT NULL,
	actor_id             STRING      NOT NULL,
	loan_account_id      STRING      NULL,
	loan_type            STRING      NOT NULL,
	ifsc_code            STRING      NULL,
	loan_amount          JSONB       NOT NULL DEFAULT '{}':::JSONB,
	disbursed_amount     JSONB       NOT NULL DEFAULT '{}':::JSONB,
	outstanding_amount   JSONB       NOT NULL DEFAULT '{}':::JSONB,
	total_payable_amount JSONB       NOT NULL DEFAULT '{}':::JSONB,
	loan_end_date        DATE        NULL,
	maturity_date        DATE        NULL,
	vendor               STRING      NOT NULL,
	details              JSONB       NULL,
	status               STRING      NOT NULL,
	created_at           TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at           TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at           TIMESTAMPTZ NULL,

	PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_loan_account_id_and_ifsc_code_vendor (loan_account_id, ifsc_code, vendor),
	FAMILY "frequently_updated" (disbursed_amount, outstanding_amount, total_payable_amount, details, status,
								 updated_at),
	FAMILY "seldom_updated" (loan_account_id, ifsc_code, loan_amount, loan_end_date, maturity_date, deleted_at)
);

COMMENT ON TABLE loan_accounts IS 'Table to keep a track of all the loan account by a user';
COMMENT ON COLUMN loan_accounts.actor_id IS 'actor for which loan request is created';
COMMENT ON COLUMN loan_accounts.loan_account_id IS 'Loan account number returned by vendor';
COMMENT ON COLUMN loan_accounts.loan_type IS '{"proto_type":"preapprovedloan.LoanType", "comment":"Loan Type"}';
COMMENT ON COLUMN loan_accounts.ifsc_code IS 'IFSC code returned by vendor';
COMMENT ON COLUMN loan_accounts.loan_amount IS 'Loan Amount';
COMMENT ON COLUMN loan_accounts.disbursed_amount IS 'Total amount credited to user’s account';
COMMENT ON COLUMN loan_accounts.outstanding_amount IS 'Amount remaining to be paid against the loan taken. This will keep on reducing after every EMI/Lump sum payment';
COMMENT ON COLUMN loan_accounts.total_payable_amount IS 'Total amount to be paid against the loan taken. This will include interest amount, fees';
COMMENT ON COLUMN loan_accounts.loan_end_date IS 'Loan End date';
COMMENT ON COLUMN loan_accounts.maturity_date IS 'Maturity date';
COMMENT ON COLUMN loan_accounts.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"Vendor string who provided this loan offer"}';
COMMENT ON COLUMN loan_accounts.details IS '{"proto_type":"preapprovedloan.LoanAccountDetails", "comment":"Details provided by vendor for the loan"}';
COMMENT ON COLUMN loan_accounts.status IS '{"proto_type":"preapprovedloan.LoanAccountStatus", "comment":"active/closed/pre-closed"}';

-- 4. Loan Requests
CREATE TABLE IF NOT EXISTS loan_requests
(
	id                STRING      NOT NULL,
	actor_id          STRING      NOT NULL,
	offer_id          STRING      NULL,
	orch_id           STRING      NULL,
	loan_account_id   STRING      NULL,
	vendor_request_id STRING      NULL,
	vendor            STRING      NOT NULL,
	details           JSONB       NULL,
	type              STRING      NULL,
	status            STRING      NOT NULL,
	sub_status        STRING      NOT NULL,
	created_at        TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at        TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at        TIMESTAMPTZ NULL,
	completed_at      TIMESTAMPTZ NULL,
	next_action       JSONB       NULL,

	PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_loan_requests_actor_vendor_active_request (actor_id ASC, vendor ASC) WHERE completed_at IS NULL,
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, actor_id, orch_id, vendor, type, created_at,
							   completed_at),
	FAMILY seldom_updated (vendor_request_id, deleted_at, loan_account_id, offer_id)
);

COMMENT ON TABLE loan_requests IS 'Table to keep a track of all the loan applications that have been raised with the vendor and to pre-close a loan account with vendor';
COMMENT ON COLUMN loan_requests.actor_id IS 'actor for which loan request is created';
COMMENT ON COLUMN loan_requests.offer_id IS 'Unique id returned by vendor';
COMMENT ON COLUMN loan_requests.orch_id IS 'Client request ID used for loan request orchestration purpose';
COMMENT ON COLUMN loan_requests.loan_account_id IS 'Loan account associated with a loan request';
COMMENT ON COLUMN loan_requests.vendor_request_id IS 'ID to uniquely identify a loan request at vendor';
COMMENT ON COLUMN loan_requests.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"Vendor string who provided this loan offer"}';
COMMENT ON COLUMN loan_requests.details IS '{"proto_type":"preapprovedloan.LoanRequestDetails", "comment":"Details passed to vendor while creating the request and info got in response"}';
COMMENT ON COLUMN loan_requests.type IS '{"proto_type":"preapprovedloan.LoanRequestType", "comment":"Creation, Closure"}';
COMMENT ON COLUMN loan_requests.status IS '{"proto_type":"preapprovedloan.LoanRequestStatus", "comment":"status of the request"}';
COMMENT ON COLUMN loan_requests.sub_status IS '{"proto_type":"preapprovedloan.LoanRequestSubStatus", "comment":"Granular info on status"}';
COMMENT ON COLUMN loan_requests.next_action IS 'next action provides the deeplink name for the next screen';

-- 5. Loan Step Executions
CREATE TABLE IF NOT EXISTS loan_step_executions
(
	id           STRING      NOT NULL,
	actor_id     STRING      NOT NULL,
	ref_id       STRING      NOT NULL,
	orch_id      STRING      NULL,
	flow         STRING      NOT NULL,
	step_name    STRING      NOT NULL,
	details      JSONB       NULL,
	status       STRING      NOT NULL,
	sub_status   STRING      NOT NULL,
	staled_at    TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	created_at   TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at   TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at   TIMESTAMPTZ NULL,
	group_stage  STRING      NULL,
	PRIMARY KEY (id ASC),
	INDEX loan_step_executions_updated_at (updated_at DESC),
	INDEX loan_step_executions_reference_id (ref_id ASC),
	UNIQUE INDEX loan_step_executions_unique_index_on_orch_id (orch_id ASC),
	UNIQUE INDEX unique_loan_step_executions_ref_id_step_name_active_execution (ref_id ASC, step_name ASC) WHERE completed_at IS NULL,
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, actor_id, ref_id, orch_id, flow, step_name,
							   created_at),
	FAMILY seldom_updated (completed_at, staled_at, deleted_at)
);

COMMENT ON TABLE loan_step_executions IS 'Table to keep a track of all the steps in verification for loan';
COMMENT ON COLUMN loan_step_executions.ref_id IS 'Ref to the entity for which step is executing';
COMMENT ON COLUMN loan_step_executions.flow IS '{"proto_type":"preapprovedloan.LoanStepExecutionFlow", "comment":"Either loan application/loan closure/offers"}';
COMMENT ON COLUMN loan_step_executions.orch_id IS 'Orchestration identifier which has started this execution';
COMMENT ON COLUMN loan_step_executions.step_name IS '{"proto_type":"preapprovedloan.LoanStepExecutionStep", "comment":"Step specific details"}';
COMMENT ON COLUMN loan_step_executions.details IS '{"proto_type":"preapprovedloan.LoanStepExecutionDetails", "comment":"Details passed to vendor while creating the request and info got in response"}';
COMMENT ON COLUMN loan_step_executions.status IS '{"proto_type":"preapprovedloan.LoanStepExecutionStatus", "comment":"status of the request"}';
COMMENT ON COLUMN loan_step_executions.sub_status IS '{"proto_type":"preapprovedloan.LoanStepExecutionSubStatus", "comment":"Granular info on status"}';
COMMENT ON COLUMN loan_step_executions.staled_at IS 'will be used to make step stale so that re-execution of the step can be done';
COMMENT ON COLUMN loan_step_executions.completed_at IS 'will be used to mark step completed at and note the time with respect to it';
COMMENT ON COLUMN loan_step_executions.group_stage IS 'group stage to which the loan step belongs';

-- 6. Loan Activities
CREATE TABLE IF NOT EXISTS loan_activities
(
	id              STRING      NOT NULL,
	loan_account_id STRING      NULL,
	details         JSONB       NULL,
	type            STRING      NULL,
	created_at      TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at      TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at      TIMESTAMPTZ NULL,
	reference_id    STRING      NULL,
	PRIMARY KEY (id ASC),
	INDEX loan_activities_updated_at (updated_at DESC),
	INDEX loan_activities_loan_account_id (loan_account_id ASC),
	UNIQUE INDEX loan_activities_unique_reference_id_type (reference_id ASC, type ASC),
	FAMILY "frequently_updated" (details, type, updated_at),
	FAMILY "seldom_updated" (deleted_at, loan_account_id)
);

COMMENT ON TABLE loan_activities IS 'Table to keep a track of any activity related to a loan account such as an EMI Payment, Lump-sum payment, penalty fee payment, etc. and not just Fi initiated, but from external vendors too';
COMMENT ON COLUMN loan_activities.loan_account_id IS 'Loan account associated with the loan';
COMMENT ON COLUMN loan_activities.details IS '{"proto_type":"preapprovedloan.LoanActivitiesDetails", "comment":""}';
COMMENT ON COLUMN loan_activities.type IS '{"proto_type":"preapprovedloan.LoanActivitiesType", "comment":""}';
COMMENT ON COLUMN loan_activities.reference_id IS 'reference id of the loan activity, could be transaction id for financial activities';

-- 7. Loan Installment Info
CREATE TABLE IF NOT EXISTS loan_installment_info
(
	id                      STRING      NOT NULL,
	account_id              STRING      NOT NULL,
	total_amount            JSONB       NOT NULL DEFAULT '{}':::JSONB,
	start_date              DATE        NOT NULL,
	end_date                DATE        NOT NULL,
	total_installment_count INT8        NOT NULL,
	next_installment_date   DATE        NULL,
	details                 JSONB       NULL,
	status                  STRING      NOT NULL,
	deactivated_at          TIMESTAMPTZ NULL,
	created_at              TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at              TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at              TIMESTAMPTZ NULL,

	PRIMARY KEY (id ASC),
	INDEX loan_installment_info_updated_at (updated_at DESC),
	INDEX loan_installment_info_account_id (account_id ASC),
	INDEX loan_installment_info_next_installment_date (next_installment_date DESC),
	FAMILY "frequently_updated" (total_installment_count, next_installment_date, details, status, updated_at),
	FAMILY "seldom_updated" (account_id, total_amount, start_date, end_date, created_at, deleted_at)
);

COMMENT ON TABLE loan_installment_info IS 'Represents the entity holding high level information related to the Installments associated with a loan account. It will also hold historical information if the installment details/schedule for a loan account is changed before the loan end date';
COMMENT ON COLUMN loan_installment_info.account_id IS 'In case of loan this will be loan account id';
COMMENT ON COLUMN loan_installment_info.total_amount IS 'Total amount to be collected through EMI';
COMMENT ON COLUMN loan_installment_info.total_installment_count IS 'Total installment count under this schedule/info';
COMMENT ON COLUMN loan_installment_info.details IS '{"proto_type":"preapprovedloan.LoanInstallmentInfoDetails", "comment":"Additional info in like payout mode, penalty rate, schedule etc"}';
COMMENT ON COLUMN loan_installment_info.status IS '{"proto_type":"preapprovedloan.LoanInstallmentInfoStatus", "comment":"active/completed/closed"}';

-- 8. Loan Installment Payout
CREATE TABLE IF NOT EXISTS loan_installment_payout
(
	id                       STRING      NOT NULL,
	loan_installment_info_id STRING      NOT NULL,
	amount                   JSONB       NOT NULL DEFAULT '{}':::JSONB,
	due_date                 DATE        NOT NULL,
	payout_date              DATE        NOT NULL,
	details                  JSONB       NULL     DEFAULT '{}':::JSONB,
	status                   STRING      NOT NULL,
	created_at               TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at               TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at               TIMESTAMPTZ NULL,

	PRIMARY KEY (id ASC),
	INDEX loan_installment_payout_updated_at (updated_at DESC),
	INDEX loan_installment_payout_loan_installment_info_id (loan_installment_info_id ASC)
);

COMMENT ON TABLE loan_installment_payout IS 'Represents an installment that has been paid to the loan account or was supposed to be paid, but failed';
COMMENT ON COLUMN loan_installment_payout.loan_installment_info_id IS 'Reference to loan_installment_info table';
COMMENT ON COLUMN loan_installment_payout.amount IS 'Total installment amount paid';
COMMENT ON COLUMN loan_installment_payout.due_date IS 'Date on which installment needs to be paid';
COMMENT ON COLUMN loan_installment_payout.payout_date IS 'Date on which installment was actually paid';
COMMENT ON COLUMN loan_installment_payout.details IS '{"proto_type":"preapprovedloan.LoanInstallmentPayoutDetails", "comment":"Additional info like actual EMI amount, penalty etc"}';
COMMENT ON COLUMN loan_installment_payout.status IS '{"proto_type":"preapprovedloan.LoanInstallmentPayoutStatus", "comment":"pending/processing/success/failed"}';

-- 9. Loan Payment Requests
CREATE TABLE IF NOT EXISTS loan_payment_requests
(
	id         STRING      NOT NULL,
	actor_id   STRING      NOT NULL,
	account_id STRING      NOT NULL,
	amount     JSONB       NOT NULL DEFAULT '{}':::JSONB,
	type       STRING      NOT NULL,
	orch_id    STRING      NOT NULL,
	details    JSONB       NULL,
	status     STRING      NOT NULL,
	sub_status STRING      NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,

	PRIMARY KEY (id ASC),
	INDEX loan_payment_requests_updated_at (updated_at DESC),
	INDEX loan_payment_requests_index_on_account_id (account_id ASC),
	UNIQUE INDEX loan_payment_requests_unique_index_on_orch_id (orch_id ASC),
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, actor_id, account_id, orch_id, created_at),
	FAMILY seldom_updated (type, amount, deleted_at)
);

COMMENT ON TABLE loan_payment_requests IS 'Table used for orchestrating payments against a loan account, be it EMIs or Lump sum payments';
COMMENT ON COLUMN loan_payment_requests.account_id IS 'Loan account against which we are making a payment';
COMMENT ON COLUMN loan_payment_requests.amount IS 'Loan Payment Amount';
COMMENT ON COLUMN loan_payment_requests.orch_id IS 'Orchestration identifier which has started this execution';
COMMENT ON COLUMN loan_payment_requests.type IS '{"proto_type":"preapprovedloan.LoanPaymentRequestType", "comment":"emi/lumpsum"}';
COMMENT ON COLUMN loan_payment_requests.details IS '{"proto_type":"preapprovedloan.LoanPaymentRequestDetails", "comment":"Additional info in transactions. Will hold payment ref ids for both remitter and beneficiary"}';
COMMENT ON COLUMN loan_payment_requests.status IS '{"proto_type":"preapprovedloan.LoanPaymentRequestStatus", "comment":"status of the request"}';
COMMENT ON COLUMN loan_payment_requests.sub_status IS '{"proto_type":"preapprovedloan.LoanPaymentRequestSubStatus", "comment":"Granular info on status"}';

-- 10. Loan Applicants
CREATE TABLE IF NOT EXISTS loan_applicants
(
	id                  STRING      NOT NULL,
	actor_id            STRING      NOT NULL,
	vendor              STRING      NOT NULL,
	vendor_applicant_id STRING      NULL,
	vendor_request_id   STRING      NULL,
	loan_program        STRING      NULL,
	status              STRING      NOT NULL,
	sub_status          STRING      NOT NULL,
	created_at          TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at          TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at          TIMESTAMPTZ NULL,

	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX loan_applicants_idx_updated_at (updated_at DESC),
	UNIQUE INDEX unique_loan_applicants_actor_vendor_loan_program (actor_id ASC, vendor ASC, loan_program ASC)
);
