DROP INDEX IF EXISTS loan_applicants@unique_loan_applicants_actor_vendor_loan_program CASCADE;
ALTER TABLE IF EXISTS loan_applicants ADD CONSTRAINT IF NOT EXISTS unique_loan_applicants_actor_vendor_loan_program_status UNIQUE(actor_id ASC, vendor ASC, loan_program ASC)
	WHERE status IN ('LOAN_APPLICANT_STATUS_CREATED','LOAN_APPLICANT_STATUS_APPROVED');
ALTER TABLE IF EXISTS loan_applicants ADD CONSTRAINT IF NOT EXISTS unique_vendor_vendor_request_id UNIQUE (vendor_request_id ASC, vendor ASC);
