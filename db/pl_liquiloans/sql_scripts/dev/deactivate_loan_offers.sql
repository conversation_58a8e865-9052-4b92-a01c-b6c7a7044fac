--- SQL script to deactivate loan offers
--- disable <PERSON><PERSON><PERSON>'s liquiloans loan offer ("PALO2XKaZ5B7RCWy4Nw3VYmTkg230828==") for IDFC CUG testing
--- disable <PERSON><PERSON><PERSON>'s liquiloans loan offer ("PALOb/Vv23wASj2EUeGegcpnqw230828==") for IDFC CUG testing

UPDATE loan_offers
SET deactivated_at = current_timestamp, updated_at = current_timestamp
WHERE id IN ('PALO2XKaZ5B7RCWy4Nw3VYmTkg230828==', 'PALOb/Vv23wASj2EUeGegcpnqw230828==') AND deactivated_at IS NULL;
