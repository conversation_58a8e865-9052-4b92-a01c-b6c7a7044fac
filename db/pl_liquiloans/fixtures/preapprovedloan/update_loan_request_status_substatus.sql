-- Fixture to update status and substatus of loan_requests table by id
-- already executed for PALRg0/ecFZQSletEd7H1EFS7A230406== (Prasoon) 'PALRKZVDgQDjTuOs9cTiHBsAgA230414==', PALRLdKSlofOTWShY7ObXtzN+w230417==, 'PALRHLkyYlHQQmic8gzmEMBdIA230420=='
-- PALRwnXAw36sRIO59t5xdn/E3A230427== (hitesh), 'PALRteo7CMfTRjWvL/y65gHZkw230502==','PALRSsHElqI5QoGBc2Cg+SG48Q230504==', 'PALR+3wR8zUoSCW6R2Cl0yPvow230503==','PALRKiq0RCESS/uNAuLdFClrKw230504==','PALR+3wR8zUoSCW6R2Cl0yPvow230503==','PALRMzN9meGNTIygayo1Z80gsg230505==','PALR+eWeNhuGRi6d7uK7BqyP7Q230505==', 'PALRcesJP/ckQCKi4l6PB22QNA230507==','PALRtYFZrDxgS1mvXE6q8ww/Bg230505=='
-- 'PALRuCWppbk7R665ahfpi/S6HQ230527==' 'PALROdkum1hGTZW4FsjjGb5qIw230522=='
update loan_requests
set status       = 'LOAN_REQUEST_STATUS_CANCELLED',
	sub_status   = 'LOAN_REQUEST_SUB_STATUS_CANCELLED',
	completed_at = now()
where id IN ('PALRA/IICBC1SQGu/8s70XOCbQ230530==');
