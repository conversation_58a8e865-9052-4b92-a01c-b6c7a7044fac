-- Backfill start date field for mandate execution in salary lite mandate request table

UPDATE salary_lite_mandate_requests
SET start_date='2024-03-12 18:30:00.000000 +00:00',
	updated_at = now()
WHERE ID = 'ab768ee2-2b99-4bbf-a002-51283b7b1c71'; -- User: @Nigam

UPDATE salary_lite_mandate_requests
SET start_date='2024-03-20 18:30:00.000000 +00:00',
	updated_at = now()
WHERE ID = '1c42789d-6295-468b-95d7-790f5c75585c'; -- User: @Shashwat

UPDATE salary_lite_mandate_requests
SET start_date='2024-03-26 18:30:00.000000 +00:00',
	updated_at = now()
WHERE ID = '14ede0ff-6f81-4170-a01b-33352cc04ca2'; -- User: @Sresth

UPDATE salary_lite_mandate_requests
SET start_date='2024-03-20 18:30:00.000000 +00:00',
	updated_at = now()
WHERE ID = '721bb4f1-ca7d-481c-903f-429915da8150'; -- User: @Yuvraj

UPDATE salary_lite_mandate_requests
SET start_date= created_at,
	updated_at = now()
WHERE request_status != 'SUCCESS'; -- Impact: update created_at for all the requests that are in non SUCCESS state
