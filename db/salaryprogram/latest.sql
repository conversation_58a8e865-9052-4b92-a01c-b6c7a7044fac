CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.aa_salary_criteria (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    criteria_name character varying NOT NULL,
    details jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    executed_at timestamp with time zone
);
COMMENT ON TABLE public.aa_salary_criteria IS 'stores criteria regarding aa salary bands and cashback';
COMMENT ON COLUMN public.aa_salary_criteria.criteria_name IS '{"proto_type":"salaryprogram.aa.CriteriaName", "comment":"aa salary criteria name"}';
COMMENT ON COLUMN public.aa_salary_criteria.details IS '{"proto_type":"salaryprogram.aa.Criterion", "comment":"stores the criteria details"}';
CREATE TABLE public.aa_salary_txn_verification_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    transaction_details jsonb,
    verified_by character varying,
    request_source character varying NOT NULL,
    verification_status character varying NOT NULL,
    salary_amount_committed jsonb NOT NULL,
    salary_estimations_ref_id uuid NOT NULL,
    aa_salary_criteria_ref_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    salary_band character varying NOT NULL,
    salary_program_registrations_ref_id character varying NOT NULL
);
COMMENT ON TABLE public.aa_salary_txn_verification_requests IS 'store verification request for aa salary flow';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.transaction_details IS '{"proto_type":"salaryprogram.aa.TransactionDetails", "comment":"stores details of credit transactions used to mark the verification as complete"}';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.verified_by IS '{"proto_type":"salaryprogram.aa.AASalaryTxnVerificationRequestVerifiedBy", "comment":"stores verifier of the credit transaction"}';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.request_source IS '{"proto_type":"salaryprogram.aa.AASalaryTxnVerificationRequestSource", "comment":"stores how verification request was initiated"}';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.verification_status IS '{"proto_type":"salaryprogram.aa.AASalaryTxnVerificationRequestStatus", "comment":"stores status of verification request"}';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.salary_amount_committed IS '{"proto_type":"google.type.Money", "comment":"stores the amount user is expected to transfer each month"}';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.salary_estimations_ref_id IS 'foreign key to id of salary_estimations table';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.aa_salary_criteria_ref_id IS 'foreign key to id of aa_salary criteria';
COMMENT ON COLUMN public.aa_salary_txn_verification_requests.salary_band IS '{"proto_type":"salaryprogram.enums.SalaryBand", "comment":"store salary band calculated for user based on salary estimated, salary committed and criteria"}';
CREATE TABLE public.dynamic_ui_element_evaluator_configs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    dynamic_ui_screen character varying DEFAULT 'DYNAMIC_UI_SCREEN_UNSPECIFIED'::character varying NOT NULL,
    dynamic_ui_usecase character varying DEFAULT 'DYNAMIC_UI_USECASE_UNSPECIFIED'::character varying NOT NULL,
    evaluator_expression_config jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.dynamic_ui_element_evaluator_configs IS 'table for defining config for controlling defined ui elements on multiple screens';
COMMENT ON COLUMN public.dynamic_ui_element_evaluator_configs.dynamic_ui_screen IS '{"proto_type":"salaryprogram.dynamic_ui_element.DynamicUIScreen", "comment":"screen for the ui content"}';
COMMENT ON COLUMN public.dynamic_ui_element_evaluator_configs.dynamic_ui_usecase IS '{"proto_type":"salaryprogram.dynamic_ui_element.DynamicUIUsecase", "comment":"usecase for the ui content"}';
COMMENT ON COLUMN public.dynamic_ui_element_evaluator_configs.evaluator_expression_config IS 'config to evaluate expression and figure out the ui variant to show';
CREATE TABLE public.dynamic_ui_elements (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    variant_name character varying NOT NULL,
    content_json jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.dynamic_ui_elements IS 'table for storing json of different ui content which can rendered on different screens+ usecases';
COMMENT ON COLUMN public.dynamic_ui_elements.variant_name IS 'name for the ui content config';
COMMENT ON COLUMN public.dynamic_ui_elements.content_json IS 'json of the ui content config';
CREATE TABLE public.health_insurance_policy_details (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    policy_vendor character varying NOT NULL,
    vendor_policy_id character varying NOT NULL,
    vendor_parent_policy_id character varying,
    policy_issuance_request_id uuid,
    policy_active_from timestamp with time zone NOT NULL,
    policy_active_till timestamp with time zone,
    policy_metadata jsonb NOT NULL,
    policy_request_source character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.health_insurance_policy_details IS 'stores details of the issued health insurance policies';
COMMENT ON COLUMN public.health_insurance_policy_details.actor_id IS 'stores the actor to whom this policy is issued';
COMMENT ON COLUMN public.health_insurance_policy_details.policy_vendor IS '{"proto_type":"vendorgateway.Vendor", "comment":"stores the vendor who is fulfilling this policy"}';
COMMENT ON COLUMN public.health_insurance_policy_details.vendor_policy_id IS 'stores the unique identifier for a policy at vendor’s end';
COMMENT ON COLUMN public.health_insurance_policy_details.vendor_parent_policy_id IS 'stores the vendor_policy_id of initial policy which got auto-renewed, would be non-empty only for policies which were issued due to existing policy getting auto-renewed at vendor end';
COMMENT ON COLUMN public.health_insurance_policy_details.policy_issuance_request_id IS 'stores the id of policy_issuance_request which led to issuance of this policy';
CREATE TABLE public.health_insurance_policy_issuance_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    policy_vendor character varying NOT NULL,
    vendor_request_id character varying,
    request_status character varying NOT NULL,
    request_expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    policy_type character varying DEFAULT 'HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED'::character varying NOT NULL
);
COMMENT ON TABLE public.health_insurance_policy_issuance_requests IS 'stores the details of request needed for initiating a health insurance policy purchase';
COMMENT ON COLUMN public.health_insurance_policy_issuance_requests.actor_id IS 'stores the actor for whom request was created';
COMMENT ON COLUMN public.health_insurance_policy_issuance_requests.policy_vendor IS '{"proto_type":"vendorgateway.Vendor", "comment":"stores the vendor who would be fulfilling the policy request"}';
COMMENT ON COLUMN public.health_insurance_policy_issuance_requests.vendor_request_id IS 'stores the unique request id passed to vendor while purchasing the policy';
COMMENT ON COLUMN public.health_insurance_policy_issuance_requests.request_status IS '{"proto_type":"salaryprogram.healthinsurance.PolicyIssuanceRequestStatus", "comment":"stores the status of issuance request"}';
COMMENT ON COLUMN public.health_insurance_policy_issuance_requests.policy_type IS '{"proto_type":"salaryProgramPb.HealthInsurancePolicyType","comment": "stores the type of health Insurance policy for the initiated request, ex- SUPER_TOP_UP_INSURANCE, BASE_HEALTH_INSURANCE"}';
CREATE TABLE public.raise_salary_verification_by_ops_eligibility_info (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    eligibility_status character varying DEFAULT 'NEW'::character varying NOT NULL,
    last_activation_from_time timestamp with time zone,
    last_activation_till_time timestamp with time zone,
    last_verified_salary_txn_timestamp timestamp with time zone,
    last_verified_salary_txn_verified_by character varying DEFAULT 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED'::character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    registration_completion_time timestamp with time zone
);
COMMENT ON TABLE public.raise_salary_verification_by_ops_eligibility_info IS 'stores the info required for checking the eligibility of actor for raising salary verification request by ops';
COMMENT ON COLUMN public.raise_salary_verification_by_ops_eligibility_info.actor_id IS 'denotes the actor for which eligibility details are stored';
COMMENT ON COLUMN public.raise_salary_verification_by_ops_eligibility_info.eligibility_status IS '{"proto_type":"salaryprogram.UserSalaryVerificationEligibilityStatus", "comment":"denotes the status given to users based on the eligibility for raising manual salary verification"}';
COMMENT ON COLUMN public.raise_salary_verification_by_ops_eligibility_info.last_activation_from_time IS 'denotes the time from when the user is salary program active due to last activation entry';
COMMENT ON COLUMN public.raise_salary_verification_by_ops_eligibility_info.last_activation_till_time IS 'denotes the time till when the user is salary program active due to last activation entry';
COMMENT ON COLUMN public.raise_salary_verification_by_ops_eligibility_info.last_verified_salary_txn_timestamp IS 'denotes the last verified salary txn timestamp';
COMMENT ON COLUMN public.raise_salary_verification_by_ops_eligibility_info.last_verified_salary_txn_verified_by IS '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestVerifiedBy", "comment":"denotes the source by which last salary txn was verified, like OPS, SYSTEM etc"}';
COMMENT ON COLUMN public.raise_salary_verification_by_ops_eligibility_info.registration_completion_time IS '{"proto": "salaryprogram.RegistrationCompletionTime", "comment": "denotes time time at which the user completed salary program registration"}';
CREATE TABLE public.salary_estimations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    salary_account_source_ref_id character varying,
    salary_account_source_type character varying DEFAULT 'SALARY_ACCOUNT_SOURCE_TYPE_UNSPECIFIED'::character varying NOT NULL,
    last_estimated_amount jsonb NOT NULL,
    last_estimated_at timestamp with time zone DEFAULT now() NOT NULL,
    last_estimated_txn_timestamp timestamp with time zone,
    provenance character varying DEFAULT 'SALARY_ESTIMATION_PROVENANCE_UNSPECIFIED'::character varying NOT NULL,
    estimated_by character varying DEFAULT 'SALARY_ESTIMATED_BY_UNSPECIFIED'::character varying NOT NULL,
    status character varying DEFAULT 'SALARY_ESTIMATION_STALENESS_STATUS_TYPE_UNSPECIFIED'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON COLUMN public.salary_estimations.id IS 'Unique identifier for each record';
COMMENT ON COLUMN public.salary_estimations.actor_id IS 'Identifier for the actor associated with the salary detection';
COMMENT ON COLUMN public.salary_estimations.salary_account_source_ref_id IS 'Identifier for the AA account';
COMMENT ON COLUMN public.salary_estimations.salary_account_source_type IS '{"proto_type":"salaryprogram.SalaryAccountSourceType", "comment":"Source type of entity which is used for salary detection"}';
COMMENT ON COLUMN public.salary_estimations.last_estimated_amount IS '{"proto_type":"types.Money", "comment":"stores the last estimated amount of the salary detected, stored in JSON format"}';
COMMENT ON COLUMN public.salary_estimations.last_estimated_at IS 'Timestamp indicating when the last estimation was performed';
COMMENT ON COLUMN public.salary_estimations.last_estimated_txn_timestamp IS 'Timestamp indicating of the last transaction time';
COMMENT ON COLUMN public.salary_estimations.provenance IS '{"proto_type":"salaryprogram.SalaryEstimationProvenance", "comment":"Source or provenance of the salary detection"}';
COMMENT ON COLUMN public.salary_estimations.estimated_by IS '{"proto_type":"salaryprogram.SalaryEstimationVerifiedBy", "comment":"Identifier of the entity that verified the salary detection"}';
COMMENT ON COLUMN public.salary_estimations.status IS '{"proto_type":"salaryprogram.SalaryEstimationStalenessStatusType", "comment":"signifies the status of the staleness"}';
CREATE TABLE public.salary_lite_mandate_execution_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    recurring_payment_id character varying NOT NULL,
    request_status character varying DEFAULT 'SALARY_LITE_MANDATE_EXECUTION_REQUEST_STATUS_UNSPECIFIED'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    execution_failure_reason character varying DEFAULT 'EXECUTION_FAILURE_REASON_UNSPECIFIED'::character varying NOT NULL,
    provenance character varying DEFAULT 'REQUEST_PROVENANCE_UNSPECIFIED'::character varying NOT NULL,
    actor_id character varying,
    mandate_request_id uuid
);
COMMENT ON TABLE public.salary_lite_mandate_execution_requests IS 'stores the salary lite mandate execution request';
COMMENT ON COLUMN public.salary_lite_mandate_execution_requests.recurring_payment_id IS 'denotes the recurring payment id for which mandate execution has to be initiated';
COMMENT ON COLUMN public.salary_lite_mandate_execution_requests.request_status IS '{"proto_type":"salaryprogram.SalaryLiteMandateExecutionRequestStatus", "comment":"denotes the status of the salary lite mandate execution request"}';
COMMENT ON COLUMN public.salary_lite_mandate_execution_requests.execution_failure_reason IS '{"proto_type":"salaryprogram.SalaryLiteMandateExecutionFailureReason", "comment":"stores the failure reason if mandate execution is failed"}';
COMMENT ON COLUMN public.salary_lite_mandate_execution_requests.provenance IS '{"proto_type":"salaryprogram.SalaryLiteMandateExecutionRequestProvenance", "comment":"stores the provenance(origin/source) of the salary lite mandate execution request"}';
COMMENT ON COLUMN public.salary_lite_mandate_execution_requests.actor_id IS 'denotes the actor id of the user for which the mandate execution request has been made';
COMMENT ON COLUMN public.salary_lite_mandate_execution_requests.mandate_request_id IS 'denotes the id of salary_lite_mandate_request for which the mandate execution request has been made';
CREATE TABLE public.salary_lite_mandate_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    recurring_payment_id character varying NOT NULL,
    amount jsonb NOT NULL,
    request_status character varying DEFAULT 'SALARY_LITE_MANDATE_REQUEST_STATUS_UNSPECIFIED'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    preferred_execution_day_of_month integer NOT NULL,
    bank character varying NOT NULL,
    start_date timestamp with time zone,
    paused_by character varying
);
COMMENT ON TABLE public.salary_lite_mandate_requests IS 'stores the details of request for initiating a salary lite mandate';
COMMENT ON COLUMN public.salary_lite_mandate_requests.actor_id IS 'stores the actor for whom request is created';
COMMENT ON COLUMN public.salary_lite_mandate_requests.recurring_payment_id IS 'stores the recurring payment id';
COMMENT ON COLUMN public.salary_lite_mandate_requests.amount IS '{"proto_type":"types.Money", "comment":"stores the mandate amount"}';
COMMENT ON COLUMN public.salary_lite_mandate_requests.request_status IS '{"proto_type":"salaryprogram.SalaryLiteMandateRequestStatus", "comment":"stores the status of mandate request"}';
COMMENT ON COLUMN public.salary_lite_mandate_requests.preferred_execution_day_of_month IS 'stores the user preferred mandate execution date of month';
COMMENT ON COLUMN public.salary_lite_mandate_requests.bank IS 'stores the mandate remitter bank detailss';
COMMENT ON COLUMN public.salary_lite_mandate_requests.start_date IS 'stores the start date of the mandate, i.e. executions can happen only from this date';
COMMENT ON COLUMN public.salary_lite_mandate_requests.paused_by IS '{"proto_type":"salaryprogram.SalaryLiteMandatePausedBy", "comment":"denotes the entity by which mandate was marked as paused"}';
CREATE TABLE public.salary_program_activation_history (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    salary_program_registration_id character varying NOT NULL,
    active_from timestamp with time zone NOT NULL,
    active_till timestamp with time zone NOT NULL,
    salary_txn_verification_request_id uuid,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    activation_action character varying DEFAULT 'SALARY_TXN'::character varying NOT NULL,
    activation_action_ref_id character varying,
    reward_activated_month_year character varying
);
COMMENT ON TABLE public.salary_program_activation_history IS 'stores salary program activation history for a salary program registration i.e when and for what duration
// the salary program was marked as active. It stores a historical audit of each (re)activation';
COMMENT ON COLUMN public.salary_program_activation_history.salary_program_registration_id IS 'denotes the registration id for which activation entry is created';
COMMENT ON COLUMN public.salary_program_activation_history.active_from IS 'denotes the time from when the salary program is active due to current activation entry';
COMMENT ON COLUMN public.salary_program_activation_history.active_till IS 'denotes the time till when the salary program is active due to current activation entry';
COMMENT ON COLUMN public.salary_program_activation_history.salary_txn_verification_request_id IS 'denotes the salary txn verification request which led to re-activation of the salary program';
COMMENT ON COLUMN public.salary_program_activation_history.activation_action IS '{"proto_type":"salaryprogram.SalaryProgramActivationAction", "comment":"stores the action through which user is salary activated, ex- SALARY_TXN, MANDATE_EXECUTION"}';
COMMENT ON COLUMN public.salary_program_activation_history.activation_action_ref_id IS 'stores the reference id for salary activation action like- salary txn verification req id, mandate execution request id';
COMMENT ON COLUMN public.salary_program_activation_history.reward_activated_month_year IS 'denotes month and year for which reward will be given to user';
CREATE TABLE public.salary_program_referrals_seasons (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    display_meta jsonb NOT NULL,
    milestones jsonb,
    display_since timestamp with time zone NOT NULL,
    display_till timestamp with time zone NOT NULL,
    active_since timestamp with time zone NOT NULL,
    active_till timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.salary_program_referrals_seasons IS 'stores the details of salaryprogram referrals seasons';
COMMENT ON COLUMN public.salary_program_referrals_seasons.display_meta IS '{"proto_type":"salaryprogram.referrals.SeasonDisplayMeta", "comment":"stores the details of salaryprogram referrals seasons"}';
COMMENT ON COLUMN public.salary_program_referrals_seasons.milestones IS '{"proto_type":"salaryprogram.referrals.SeasonMilestones", "comment":"stores the milestones related details of a season"}';
COMMENT ON COLUMN public.salary_program_referrals_seasons.display_since IS 'denotes the time from when the season should be displayed on the app';
COMMENT ON COLUMN public.salary_program_referrals_seasons.display_till IS 'denotes the time till when the season should be displayed on the app';
COMMENT ON COLUMN public.salary_program_referrals_seasons.active_since IS 'denotes the time from when the season is active';
COMMENT ON COLUMN public.salary_program_referrals_seasons.active_till IS 'denotes the time till when the season is active';
CREATE TABLE public.salary_program_registration_stage_details (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    salary_program_registration_id character varying NOT NULL,
    stage_name character varying NOT NULL,
    stage_status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.salary_program_registration_stage_details IS 'stores details of stages which were initiated/completed for a salary program registration';
COMMENT ON COLUMN public.salary_program_registration_stage_details.salary_program_registration_id IS 'denotes the salary program registration for which stage details are present';
COMMENT ON COLUMN public.salary_program_registration_stage_details.stage_name IS '{"proto_type":"salaryprogram.SalaryProgramRegistrationStage", "comment":"denotes the registration stage whose details are present"}';
COMMENT ON COLUMN public.salary_program_registration_stage_details.stage_status IS '{"proto_type":"salaryprogram.SalaryProgramRegistrationStageStatus", "comment":"denotes the current status of registration stage"}';
CREATE TABLE public.salary_program_registrations (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    account_id character varying NOT NULL,
    registration_flow_version character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    completed_at timestamp with time zone,
    account_type character varying DEFAULT 'SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_UNSPECIFIED'::character varying NOT NULL,
    registration_flow_type character varying DEFAULT 'SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_UNSPECIFIED'::character varying NOT NULL
);
COMMENT ON TABLE public.salary_program_registrations IS 'stores the registration of a user into the salary program';
COMMENT ON COLUMN public.salary_program_registrations.actor_id IS 'denotes the actor whose account is getting registered for the salary program';
COMMENT ON COLUMN public.salary_program_registrations.account_id IS 'denotes the internal id of the account which is getting registered for the salary program';
COMMENT ON COLUMN public.salary_program_registrations.registration_flow_version IS '{"proto_type":"salaryprogram.SalaryProgramRegistrationFlowVersion", "comment":"denotes the version of registration flow used for registering for the salary program"}';
COMMENT ON COLUMN public.salary_program_registrations.completed_at IS 'stores the time at which salary program registration is completed';
COMMENT ON COLUMN public.salary_program_registrations.account_type IS '{"proto_type":"salaryprogram.SalaryProgramRegistrationAccountType", "comment":"SalaryProgramRegistrationAccountType indicates the type of account for which the salary program flows registration was done"}';
COMMENT ON COLUMN public.salary_program_registrations.registration_flow_type IS '{"proto_type":"salaryprogram.SalaryProgramRegistrationFlowType", "comment":"SalaryProgramRegistrationFlowType indicates the flow type for which the registration was done."}';
CREATE TABLE public.salary_txn_verification_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    txn_id character varying NOT NULL,
    request_source character varying NOT NULL,
    salary_txn_verification_version character varying NOT NULL,
    verification_status character varying NOT NULL,
    verification_sub_status character varying NOT NULL,
    verified_by character varying,
    txn_employer_id character varying,
    txn_timestamp timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    user_ack_status character varying DEFAULT 'ACKNOWLEDGEMENT_STATUS_UNSPECIFIED'::character varying NOT NULL,
    verification_failure_reason_category character varying DEFAULT 'FAILURE_REASON_CATEGORY_UNSPECIFIED'::character varying NOT NULL,
    verification_failure_reason_sub_category character varying DEFAULT 'FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED'::character varying NOT NULL,
    verification_remark character varying,
    auto_verifier_meta jsonb
);
COMMENT ON TABLE public.salary_txn_verification_requests IS 'stores the requests raised for a salary txn verification';
COMMENT ON COLUMN public.salary_txn_verification_requests.actor_id IS 'denotes the actor whose txn needs to be verified';
COMMENT ON COLUMN public.salary_txn_verification_requests.txn_id IS 'denotes the txn which needs to be verified';
COMMENT ON COLUMN public.salary_txn_verification_requests.request_source IS '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestSource", "comment":"denotes the source from which the verification request was raised"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.salary_txn_verification_version IS '{"proto_type":"salaryprogram.SalaryTxnVerificationVersion", "comment":"denotes the version of salary txn verification that is used to verify the current request"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.verification_status IS '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestStatus", "comment":"denotes the current status of verification"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.verification_sub_status IS '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestSubStatus", "comment":"denotes the current sub-status of verification"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.verified_by IS '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestVerifiedBy", "comment":"denotes who verified the request"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.txn_employer_id IS 'denotes the internal id of employer who had initiated the salary txn';
COMMENT ON COLUMN public.salary_txn_verification_requests.txn_timestamp IS 'denotes the txn timestamp, helpful for sorting verification request based on txn recency';
COMMENT ON COLUMN public.salary_txn_verification_requests.user_ack_status IS '{"proto_type":"salaryprogram.AcknowledgmentStatus", "comment":"useful for marking that the status of a given verification request was acknowledged by the user"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.verification_failure_reason_category IS '{"proto": "salaryprogram.SalaryTxnVerificationFailureReasonCategory", "comment": "stores the verification failure reason category if the salary txn verification is failed"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.verification_failure_reason_sub_category IS '{"proto": "salaryprogram.SalaryTxnVerificationFailureReasonSubCategory", "comment": "stores the verification failure reason sub-category if the salary txn verification is failed"}';
COMMENT ON COLUMN public.salary_txn_verification_requests.verification_remark IS 'stores the salary txn verification remark';
COMMENT ON COLUMN public.salary_txn_verification_requests.auto_verifier_meta IS 'stores the metadata of checks ran on txn by in-house salary auto verifier';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.whitelisted_b2b_users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    phone_number character varying NOT NULL,
    employer_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    hrms_management_vendor character varying,
    vendor_employer_id character varying,
    vendor_user_id character varying
);
COMMENT ON TABLE public.whitelisted_b2b_users IS 'stores the details of users whitelisted for b2b salary program, we perform some chores for whitelisted users like- automatically register employer for salary program for the user, etc';
COMMENT ON COLUMN public.whitelisted_b2b_users.phone_number IS 'denotes the phone number of the user with country code, ex- ************';
COMMENT ON COLUMN public.whitelisted_b2b_users.employer_id IS 'denotes the id of the employer of the user while whitelisting';
COMMENT ON COLUMN public.whitelisted_b2b_users.hrms_management_vendor IS '{"proto_type":"vendorgateway.Vendor", "comment":"denotes the vendor which will be used for user HRMS management, ex- update user salary account bank details on the hrms"}';
COMMENT ON COLUMN public.whitelisted_b2b_users.vendor_employer_id IS 'denotes the id of the user employer on the vendor end';
COMMENT ON COLUMN public.whitelisted_b2b_users.vendor_user_id IS 'denotes the user id on the vendor end';
ALTER TABLE ONLY public.aa_salary_criteria
    ADD CONSTRAINT aa_salary_criteria_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_salary_txn_verification_requests
    ADD CONSTRAINT aa_salary_txn_verification_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.dynamic_ui_elements
    ADD CONSTRAINT dynamic_ui_elements_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.health_insurance_policy_details
    ADD CONSTRAINT health_insurance_policy_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.health_insurance_policy_issuance_requests
    ADD CONSTRAINT health_insurance_policy_issuance_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.raise_salary_verification_by_ops_eligibility_info
    ADD CONSTRAINT raise_salary_verification_by_ops_eligibility_info_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_estimations
    ADD CONSTRAINT salary_estimations_id_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_lite_mandate_execution_requests
    ADD CONSTRAINT salary_lite_mandate_execution_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_lite_mandate_requests
    ADD CONSTRAINT salary_lite_mandate_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_program_activation_history
    ADD CONSTRAINT salary_program_activation_history_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_program_referrals_seasons
    ADD CONSTRAINT salary_program_referrals_seasons_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_program_registration_stage_details
    ADD CONSTRAINT salary_program_registration_stage_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_program_registrations
    ADD CONSTRAINT salary_program_registrations_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salary_txn_verification_requests
    ADD CONSTRAINT salary_txn_verification_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.whitelisted_b2b_users
    ADD CONSTRAINT whitelisted_b2b_users_pkey PRIMARY KEY (id);
CREATE INDEX aa_sal_txn_ver_reqs_salary_program_registrations_ref_id_fkey ON public.aa_salary_txn_verification_requests USING btree (salary_program_registrations_ref_id);
CREATE INDEX aa_salary_txn_verification_requests_actor_id_idx ON public.aa_salary_txn_verification_requests USING btree (actor_id);
CREATE INDEX health_ins_policy_details_actor_id_policy_active_till_from_idx ON public.health_insurance_policy_details USING btree (actor_id, policy_active_till, policy_active_from);
CREATE UNIQUE INDEX health_ins_policy_details_policy_issuance_req_id_unique_idx ON public.health_insurance_policy_details USING btree (policy_issuance_request_id);
CREATE INDEX health_ins_policy_details_updated_at_idx ON public.health_insurance_policy_details USING btree (updated_at);
CREATE UNIQUE INDEX health_ins_policy_details_vendor_vendor_policy_id_unique_idx ON public.health_insurance_policy_details USING btree (policy_vendor, vendor_policy_id);
CREATE INDEX health_ins_policy_issuance_req_actor_id_req_status_idx ON public.health_insurance_policy_issuance_requests USING btree (actor_id, request_status);
CREATE INDEX health_ins_policy_issuance_req_updated_at_idx ON public.health_insurance_policy_issuance_requests USING btree (updated_at);
CREATE UNIQUE INDEX health_ins_policy_issuance_req_vendor_vendor_req_id_unique_idx ON public.health_insurance_policy_issuance_requests USING btree (policy_vendor, vendor_request_id);
CREATE INDEX health_insurance_policy_details_active_till_from_idx ON public.health_insurance_policy_details USING btree (policy_active_till, policy_active_from);
CREATE INDEX raise_salary_ver_by_ops_eligibility_info_activation_till_idx ON public.raise_salary_verification_by_ops_eligibility_info USING btree (last_activation_till_time);
CREATE UNIQUE INDEX raise_salary_ver_by_ops_eligibility_info_actor_id_unique_idx ON public.raise_salary_verification_by_ops_eligibility_info USING btree (actor_id);
CREATE INDEX raise_salary_ver_by_ops_eligibility_info_reg_compl_time_idx ON public.raise_salary_verification_by_ops_eligibility_info USING btree (registration_completion_time);
CREATE INDEX raise_salary_ver_by_ops_eligibility_info_updated_at_idx ON public.raise_salary_verification_by_ops_eligibility_info USING btree (updated_at);
CREATE INDEX salary_estimations_actor_id_idx ON public.salary_estimations USING btree (actor_id);
CREATE UNIQUE INDEX salary_estimations_actor_id_source_type_status_stale_uniq_idx ON public.salary_estimations USING btree (actor_id, salary_account_source_type) WHERE ((status)::text = 'SALARY_ESTIMATION_STALENESS_STATUS_TYPE_LATEST'::text);
CREATE INDEX salary_estimations_updated_at_idx ON public.salary_estimations USING btree (updated_at DESC);
CREATE INDEX salary_lite_mandate_execution_requests_actor_id_index ON public.salary_lite_mandate_execution_requests USING btree (actor_id);
CREATE INDEX salary_lite_mandate_execution_requests_recurring_payment_id_idx ON public.salary_lite_mandate_execution_requests USING btree (recurring_payment_id);
CREATE INDEX salary_lite_mandate_execution_requests_updated_at_idx ON public.salary_lite_mandate_execution_requests USING btree (updated_at);
CREATE INDEX salary_lite_mandate_req_actor_id_idx ON public.salary_lite_mandate_requests USING btree (actor_id);
CREATE INDEX salary_lite_mandate_req_preferred_execution_day_of_month_idx ON public.salary_lite_mandate_requests USING btree (preferred_execution_day_of_month);
CREATE UNIQUE INDEX salary_lite_mandate_req_recurring_payment_id_unique_idx ON public.salary_lite_mandate_requests USING btree (recurring_payment_id);
CREATE INDEX salary_lite_mandate_req_updated_at_idx ON public.salary_lite_mandate_requests USING btree (updated_at);
CREATE UNIQUE INDEX salary_program_act_history_activation_action_ref_id_unique_idx ON public.salary_program_activation_history USING btree (activation_action_ref_id);
CREATE UNIQUE INDEX salary_program_act_history_salary_txn_ver_req_id_unique_idx ON public.salary_program_activation_history USING btree (salary_txn_verification_request_id);
CREATE INDEX salary_program_activation_history_active_till_from_idx ON public.salary_program_activation_history USING btree (active_till, active_from);
CREATE INDEX salary_program_activation_history_reg_id_active_till_from_idx ON public.salary_program_activation_history USING btree (salary_program_registration_id, active_till, active_from);
CREATE INDEX salary_program_activation_history_updated_at_idx ON public.salary_program_activation_history USING btree (updated_at);
CREATE INDEX salary_program_referrals_seasons_active_till_since_idx ON public.salary_program_referrals_seasons USING btree (active_till, active_since);
CREATE INDEX salary_program_referrals_seasons_updated_at_idx ON public.salary_program_referrals_seasons USING btree (updated_at);
CREATE UNIQUE INDEX salary_program_reg_stage_details_reg_id_stage_name_unique_idx ON public.salary_program_registration_stage_details USING btree (salary_program_registration_id, stage_name);
CREATE INDEX salary_program_reg_stage_details_updated_at_idx ON public.salary_program_registration_stage_details USING btree (updated_at);
CREATE UNIQUE INDEX salary_program_registration_actor_id_account_id_del_null_uq_idx ON public.salary_program_registrations USING btree (actor_id, account_id) WHERE (deleted_at IS NULL);
CREATE INDEX salary_program_registration_updated_at_idx ON public.salary_program_registrations USING btree (updated_at);
CREATE INDEX salary_txn_ver_requests_actor_id_idx ON public.salary_txn_verification_requests USING btree (actor_id);
CREATE INDEX salary_txn_ver_requests_req_source_status_substatus_idx ON public.salary_txn_verification_requests USING btree (request_source, verification_status, verification_sub_status);
CREATE UNIQUE INDEX salary_txn_verification_requests_txn_id_unique_idx ON public.salary_txn_verification_requests USING btree (txn_id);
CREATE INDEX salary_txn_verification_requests_updated_at_idx ON public.salary_txn_verification_requests USING btree (updated_at);
CREATE UNIQUE INDEX whitelisted_b2b_users_phone_number_idx ON public.whitelisted_b2b_users USING btree (phone_number);
CREATE INDEX whitelisted_b2b_users_updated_at_idx ON public.whitelisted_b2b_users USING btree (updated_at);
ALTER TABLE ONLY public.aa_salary_txn_verification_requests
    ADD CONSTRAINT aa_salary_txn_verification_re_salary_program_registrations_fkey FOREIGN KEY (salary_program_registrations_ref_id) REFERENCES public.salary_program_registrations(id);
ALTER TABLE ONLY public.aa_salary_txn_verification_requests
    ADD CONSTRAINT aa_salary_txn_verification_reque_aa_salary_criteria_ref_id_fkey FOREIGN KEY (aa_salary_criteria_ref_id) REFERENCES public.aa_salary_criteria(id);
ALTER TABLE ONLY public.aa_salary_txn_verification_requests
    ADD CONSTRAINT aa_salary_txn_verification_reque_salary_estimations_ref_id_fkey FOREIGN KEY (salary_estimations_ref_id) REFERENCES public.salary_estimations(id);
ALTER TABLE ONLY public.health_insurance_policy_details
    ADD CONSTRAINT health_insurance_policy_details_policy_issuance_request_id_fkey FOREIGN KEY (policy_issuance_request_id) REFERENCES public.health_insurance_policy_issuance_requests(id);
ALTER TABLE ONLY public.salary_program_activation_history
    ADD CONSTRAINT salary_program_activation_his_salary_program_registration__fkey FOREIGN KEY (salary_program_registration_id) REFERENCES public.salary_program_registrations(id);
ALTER TABLE ONLY public.salary_program_activation_history
    ADD CONSTRAINT salary_program_activation_his_salary_txn_verification_requ_fkey FOREIGN KEY (salary_txn_verification_request_id) REFERENCES public.salary_txn_verification_requests(id);
ALTER TABLE ONLY public.salary_program_registration_stage_details
    ADD CONSTRAINT salary_program_registration_s_salary_program_registration__fkey FOREIGN KEY (salary_program_registration_id) REFERENCES public.salary_program_registrations(id);
