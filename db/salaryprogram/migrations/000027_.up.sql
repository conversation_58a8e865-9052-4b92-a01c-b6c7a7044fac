ALTER TABLE IF EXISTS salary_estimations ALTER COLUMN estimated_by SET DEFAULT 'SALARY_ESTIMATED_BY_UNSPECIFIED';

ALTER TABLE salary_estimations DROP CONSTRAINT IF EXISTS salary_estimations_actor_id_salary_account_source_type_key;

CREATE UNIQUE INDEX IF NOT EXISTS salary_estimations_actor_id_source_type_status_stale_uniq_idx ON salary_estimations (actor_id ASC, salary_account_source_type ASC) WHERE status ='SALARY_ESTIMATION_STALENESS_STATUS_TYPE_LATEST';
