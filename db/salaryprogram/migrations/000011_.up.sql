ALTER TABLE IF EXISTS raise_salary_verification_by_ops_eligibility_info
    ADD COLUMN IF NOT EXISTS registration_completion_time timestamp with time zone;

COMMENT ON COLUMN raise_salary_verification_by_ops_eligibility_info.registration_completion_time IS '{"proto": "salaryprogram.RegistrationCompletionTime", "comment": "denotes time time at which the user completed salary program registration"}';

create index if not exists raise_salary_ver_by_ops_eligibility_info_reg_compl_time_idx on raise_salary_verification_by_ops_eligibility_info using btree (registration_completion_time);
