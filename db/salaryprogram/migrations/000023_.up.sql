alter table whitelisted_b2b_users add column if not exists hrms_management_vendor varchar;
alter table whitelisted_b2b_users add column if not exists vendor_employer_id varchar;
alter table whitelisted_b2b_users add column if not exists vendor_user_id varchar;

comment on column whitelisted_b2b_users.hrms_management_vendor is '{"proto_type":"vendorgateway.Vendor", "comment":"denotes the vendor which will be used for user HRMS management, ex- update user salary account bank details on the hrms"}';
comment on column whitelisted_b2b_users.vendor_employer_id is 'denotes the id of the user employer on the vendor end';
comment on column whitelisted_b2b_users.vendor_user_id is 'denotes the user id on the vendor end';
