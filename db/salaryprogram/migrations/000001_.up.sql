CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- registrations table
create table if not exists salary_program_registrations
(
	id 							varchar not null primary key,
	actor_id 					varchar not null,
	account_id 					varchar not null,
	registration_flow_version  	varchar not null,

	created_at         			timestamp with time zone default now() not null,
	updated_at         			timestamp with time zone default now() not null,
	deleted_at         			timestamp with time zone default null
);

comment on table salary_program_registrations is 'stores the registration of a user into the salary program';
comment on column salary_program_registrations.actor_id is 'denotes the actor whose account is getting registered for the salary program';
comment on column salary_program_registrations.account_id is 'denotes the internal id of the account which is getting registered for the salary program';
comment on column salary_program_registrations.registration_flow_version is '{"proto_type":"salaryprogram.SalaryProgramRegistrationFlowVersion", "comment":"denotes the version of registration flow used for registering for the salary program"}';

-- only one registration should exist for given (actor, account) combination.
create unique index if not exists salary_program_registration_actor_id_account_id_unique_idx ON salary_program_registrations (actor_id, account_id);

-- data team need this index for periodic snapshot query
create index if not exists salary_program_registration_updated_at_idx ON salary_program_registrations USING btree (updated_at);

-- registration_stage_details table
create table if not exists salary_program_registration_stage_details
(
	id 											uuid not null  default uuid_generate_v4() primary key,
	salary_program_registration_id 				varchar not null references salary_program_registrations(id),
	stage_name 									varchar not null,
	stage_status  								varchar not null,

	created_at         							timestamp with time zone default now() not null,
	updated_at         							timestamp with time zone default now() not null,
	deleted_at         							timestamp with time zone default null
);

comment on table salary_program_registration_stage_details is 'stores details of stages which were initiated/completed for a salary program registration';
comment on column salary_program_registration_stage_details.salary_program_registration_id is 'denotes the salary program registration for which stage details are present';
comment on column salary_program_registration_stage_details.stage_name is '{"proto_type":"salaryprogram.SalaryProgramRegistrationStage", "comment":"denotes the registration stage whose details are present"}';
comment on column salary_program_registration_stage_details.stage_status is '{"proto_type":"salaryprogram.SalaryProgramRegistrationStageStatus", "comment":"denotes the current status of registration stage"}';

-- only one entry should exist for a given stage of registration.
create unique index if not exists salary_program_reg_stage_details_reg_id_stage_name_unique_idx ON salary_program_registration_stage_details (salary_program_registration_id, stage_name);

-- data team need this index for periodic snapshot query
create index if not exists salary_program_reg_stage_details_updated_at_idx ON salary_program_registration_stage_details USING btree (updated_at);
