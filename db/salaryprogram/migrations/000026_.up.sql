CREATE TABLE IF NOT EXISTS salary_estimations
(
	-- Unique identifier for each record
	id UUID NOT NULL DEFAULT UUID_GENERATE_V4(),

	-- Identifier for the actor associated with the salary detection
	actor_id VARCHAR NOT NULL,

	-- Identifier of the account considered for salary detection, e,g, AA Account,
	salary_account_source_ref_id VARCHAR,

	-- Source type of entity which is used for salary detection
	salary_account_source_type VARCHAR DEFAULT 'SALARY_ACCOUNT_SOURCE_TYPE_UNSPECIFIED' NOT NULL,

	-- Last estimated amount of the salary detected, stored in JSON format
	last_estimated_amount JSONB NOT NULL,

	-- Timestamp indicating when the last successful amount was performed
	last_estimated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

	-- Timestamp indicating the last transaction time
	last_estimated_txn_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NULL,

	-- Source or provenance of the salary detection
	provenance VARCHAR DEFAULT 'SALARY_ESTIMATION_PROVENANCE_UNSPECIFIED' NOT NULL,

	-- Identifier of the entity that estimated the salary detection
	estimated_by VARCHAR DEFAULT 'SALARY_ESTIMATION_VERIFIED_BY_UNSPECIFIED' NOT NULL,

	-- Identifier of the entity that verified the salary detection
	status VARCHAR DEFAULT 'SALARY_ESTIMATION_STALENESS_STATUS_TYPE_UNSPECIFIED' NOT NULL,

	-- Timestamp indicating when the record was created
	created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

	-- Timestamp indicating when the record was last updated
	updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

	-- Timestamp indicating when the record was deleted (if applicable)
	deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,

									CONSTRAINT salary_estimations_id_pkey PRIMARY KEY (id),
	CONSTRAINT salary_estimations_actor_id_salary_account_source_type_key UNIQUE (actor_id, salary_account_source_type)
	);

CREATE INDEX IF NOT EXISTS salary_estimations_updated_at_idx ON salary_estimations USING btree (updated_at DESC);
CREATE INDEX IF NOT EXISTS salary_estimations_actor_id_idx ON salary_estimations USING btree (actor_id ASC);

COMMENT ON COLUMN salary_estimations.id IS 'Unique identifier for each record';
COMMENT ON COLUMN salary_estimations.actor_id IS 'Identifier for the actor associated with the salary detection';
COMMENT ON COLUMN salary_estimations.salary_account_source_ref_id IS 'Identifier for the AA account';
COMMENT ON COLUMN salary_estimations.salary_account_source_type IS '{"proto_type":"salaryprogram.SalaryAccountSourceType", "comment":"Source type of entity which is used for salary detection"}';
COMMENT ON COLUMN salary_estimations.last_estimated_amount IS '{"proto_type":"types.Money", "comment":"stores the last estimated amount of the salary detected, stored in JSON format"}';
COMMENT ON COLUMN salary_estimations.last_estimated_txn_timestamp IS 'Timestamp indicating of the last transaction time';
COMMENT ON COLUMN salary_estimations.last_estimated_at IS 'Timestamp indicating when the last estimation was performed';
COMMENT ON COLUMN salary_estimations.provenance IS '{"proto_type":"salaryprogram.SalaryEstimationProvenance", "comment":"Source or provenance of the salary detection"}';
COMMENT ON COLUMN salary_estimations.estimated_by IS '{"proto_type":"salaryprogram.SalaryEstimationVerifiedBy", "comment":"Identifier of the entity that verified the salary detection"}';
COMMENT ON COLUMN salary_estimations.status IS '{"proto_type":"salaryprogram.SalaryEstimationStalenessStatusType", "comment":"signifies the status of the staleness"}';
