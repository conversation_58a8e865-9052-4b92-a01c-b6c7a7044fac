-- salary_txn_verification_requests table
create table if not exists salary_txn_verification_requests
(
	id uuid not null default uuid_generate_v4() primary key,
	actor_id varchar not null,
	txn_id varchar not null,
	request_source varchar not null,
	salary_txn_verification_version varchar not null,
	verification_status varchar not null,
	verification_sub_status varchar not null,
	verified_by varchar,
	txn_employer_id varchar,
	txn_timestamp timestamp with time zone not null,

	created_at timestamp with time zone default now() not null,
	updated_at timestamp with time zone default now() not null,
	deleted_at timestamp with time zone default null
);

comment on table salary_txn_verification_requests is 'stores the requests raised for a salary txn verification';
comment on column salary_txn_verification_requests.actor_id is 'denotes the actor whose txn needs to be verified';
comment on column salary_txn_verification_requests.txn_id is 'denotes the txn which needs to be verified';
comment on column salary_txn_verification_requests.request_source is '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestSource", "comment":"denotes the source from which the verification request was raised"}';
comment on column salary_txn_verification_requests.salary_txn_verification_version is '{"proto_type":"salaryprogram.SalaryTxnVerificationVersion", "comment":"denotes the version of salary txn verification that is used to verify the current request"}';
comment on column salary_txn_verification_requests.verification_status is '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestStatus", "comment":"denotes the current status of verification"}';
comment on column salary_txn_verification_requests.verification_sub_status is '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestSubStatus", "comment":"denotes the current sub-status of verification"}';
comment on column salary_txn_verification_requests.verified_by is '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestVerifiedBy", "comment":"denotes who verified the request"}';
comment on column salary_txn_verification_requests.txn_employer_id is 'denotes the internal id of employer who had initiated the salary txn';
comment on column salary_txn_verification_requests.txn_timestamp is 'denotes the txn timestamp, helpful for sorting verification request based on txn recency';

-- only one verification request entry should exist for a given txnId.
create unique index if not exists salary_txn_verification_requests_txn_id_unique_idx ON salary_txn_verification_requests (txn_id);

-- data team need this index for periodic snapshot query
create index if not exists salary_txn_verification_requests_updated_at_idx ON salary_txn_verification_requests USING btree (updated_at);

-- salary_program_activation_history table
create table if not exists salary_program_activation_history
(
	id uuid not null default uuid_generate_v4() primary key,
	salary_program_registration_id varchar not null references salary_program_registrations(id),
	active_from timestamp with time zone not null,
	active_till timestamp with time zone not null,
	salary_txn_verification_request_id uuid not null references salary_txn_verification_requests(id),

	created_at timestamp with time zone default now() not null,
	updated_at timestamp with time zone default now() not null,
	deleted_at timestamp with time zone default null
);

comment on table salary_program_activation_history is 'stores salary program activation history for a salary program registration i.e when and for what duration
// the salary program was marked as active. It stores a historical audit of each (re)activation';
comment on column salary_program_activation_history.salary_program_registration_id is 'denotes the registration id for which activation entry is created';
comment on column salary_program_activation_history.active_from is 'denotes the time from when the salary program is active due to current activation entry';
comment on column salary_program_activation_history.active_till is 'denotes the time till when the salary program is active due to current activation entry';
comment on column salary_program_activation_history.salary_txn_verification_request_id is 'denotes the salary txn verification request which led to re-activation of the salary program';

-- useful for optimizing activation history fetches for a given registration.
create index if not exists salary_program_activation_history_reg_id_active_from_till_idx ON salary_program_activation_history (salary_program_registration_id, active_from, active_till);

-- only one activation entry should exist for a given salary_txn_verification_request_id.
create unique index if not exists salary_program_act_history_salary_txn_ver_req_id_unique_idx ON salary_program_activation_history (salary_txn_verification_request_id);

-- data team need this index for periodic snapshot query
create index if not exists salary_program_activation_history_updated_at_idx ON salary_program_activation_history USING btree (updated_at);
