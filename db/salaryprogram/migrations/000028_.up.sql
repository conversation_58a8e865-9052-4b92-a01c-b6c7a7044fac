ALTER TABLE IF EXISTS salary_program_registrations ADD COLUMN IF NOT EXISTS account_type VARCHAR NOT NULL DEFAULT 'SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_UNSPECIFIED';
COMMENT ON COLUMN salary_program_registrations.account_type IS '{"proto_type":"salaryprogram.SalaryProgramRegistrationAccountType", "comment":"SalaryProgramRegistrationAccountType indicates the type of account for which the salary program flows registration was done"}';

ALTER TABLE IF EXISTS salary_program_registrations ADD COLUMN IF NOT EXISTS registration_flow_type VARCHAR NOT NULL DEFAULT 'SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_UNSPECIFIED';
COMMENT ON COLUMN salary_program_registrations.registration_flow_type IS '{"proto_type":"salaryprogram.SalaryProgramRegistrationFlowType", "comment":"SalaryProgramRegistrationFlowType indicates the flow type for which the registration was done."}';
