-- aa_salary_criteria table
CREATE TABLE IF NOT EXISTS aa_salary_criteria
(
    id uuid not null default uuid_generate_v4() primary key,
    criteria_name varchar not null,
    details jsonb not null,
    status varchar not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    executed_at timestamp with time zone default null
                             );

COMMENT ON TABLE aa_salary_criteria IS 'stores criteria regarding aa salary bands and cashback';
COMMENT ON COLUMN aa_salary_criteria.criteria_name IS '{"proto_type":"salaryprogram.aa.CriteriaName", "comment":"aa salary criteria name"}';
COMMENT ON COLUMN aa_salary_criteria.details IS '{"proto_type":"salaryprogram.aa.Criterion", "comment":"stores the criteria details"}';
COMMENT ON COLUMN aa_salary_criteria.status IS '{"proto_type":"salaryprogram.aa.CriteriaStatus", "comment":"status of the criteria (Active/Deprecated)"}';

-- aa_salary_txn_verification_requests table
CREATE TABLE IF NOT EXISTS aa_salary_txn_verification_requests
(
    id uuid not null default uuid_generate_v4() primary key,
    actor_id varchar not null,
    transaction_details jsonb,
    verified_by varchar,
    request_source varchar not null,
    verification_status varchar not null,
    salary_amount_committed jsonb not null,
    salary_estimations_ref_id uuid not null references salary_estimations(id),
    aa_salary_criteria_ref_id uuid not null references aa_salary_criteria(id),
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone default null
                             );

COMMENT ON TABLE aa_salary_txn_verification_requests IS 'store verification request for aa salary flow';
COMMENT ON COLUMN aa_salary_txn_verification_requests.transaction_details IS '{"proto_type":"salaryprogram.aa.TransactionDetails", "comment":"stores details of credit transactions used to mark the verification as complete"}';
COMMENT ON COLUMN aa_salary_txn_verification_requests.verified_by IS '{"proto_type":"salaryprogram.aa.AASalaryTxnVerificationRequestVerifiedBy", "comment":"stores verifier of the credit transaction"}';
COMMENT ON COLUMN aa_salary_txn_verification_requests.request_source IS '{"proto_type":"salaryprogram.aa.AASalaryTxnVerificationRequestSource", "comment":"stores how verification request was initiated"}';
COMMENT ON COLUMN aa_salary_txn_verification_requests.verification_status IS '{"proto_type":"salaryprogram.aa.AASalaryTxnVerificationRequestStatus", "comment":"stores status of verification request"}';
COMMENT ON COLUMN aa_salary_txn_verification_requests.salary_amount_committed IS '{"proto_type":"google.type.Money", "comment":"stores the amount user is expected to transfer each month"}';
COMMENT ON COLUMN aa_salary_txn_verification_requests.salary_estimations_ref_id IS 'foreign key to id of salary_estimations table';
COMMENT ON COLUMN aa_salary_txn_verification_requests.aa_salary_criteria_ref_id IS 'foreign key to id of aa_salary criteria';

CREATE INDEX aa_salary_txn_verification_requests_actor_id_idx ON aa_salary_txn_verification_requests USING btree (actor_id);