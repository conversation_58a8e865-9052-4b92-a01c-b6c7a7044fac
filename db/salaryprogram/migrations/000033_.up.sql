alter table salary_lite_mandate_execution_requests add column if not exists actor_id varchar;
alter table salary_lite_mandate_execution_requests add column if not exists mandate_request_id uuid;
CREATE INDEX  if not exists salary_lite_mandate_execution_requests_actor_id_index ON salary_lite_mandate_execution_requests USING btree (actor_id);

comment on column salary_lite_mandate_execution_requests.actor_id is 'denotes the actor id of the user for which the mandate execution request has been made';
comment on column salary_lite_mandate_execution_requests.mandate_request_id is 'denotes the id of salary_lite_mandate_request for which the mandate execution request has been made';