-- raise_salary_verification_by_ops_eligibility_info table
create table if not exists raise_salary_verification_by_ops_eligibility_info
(
	id                                   uuid    not null         default uuid_generate_v4() primary key,
	actor_id                             varchar not null,
	eligibility_status                   varchar not null         default 'NEW',
	last_activation_from_time            timestamp with time zone,
	last_activation_till_time            timestamp with time zone,
	last_verified_salary_txn_timestamp   timestamp with time zone,
	last_verified_salary_txn_verified_by varchar                  default 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED',

	created_at                           timestamp with time zone default now() not null,
	updated_at                           timestamp with time zone default now() not null,
	deleted_at                           timestamp with time zone default null
);

comment on table raise_salary_verification_by_ops_eligibility_info is 'stores the info required for checking the eligibility of actor for raising salary verification request by ops';
comment on column raise_salary_verification_by_ops_eligibility_info.actor_id is 'denotes the actor for which eligibility details are stored';
comment on column raise_salary_verification_by_ops_eligibility_info.eligibility_status is '{"proto_type":"salaryprogram.UserSalaryVerificationEligibilityStatus", "comment":"denotes the status given to users based on the eligibility for raising manual salary verification"}';
comment on column raise_salary_verification_by_ops_eligibility_info.last_activation_from_time is 'denotes the time from when the user is salary program active due to last activation entry';
comment on column raise_salary_verification_by_ops_eligibility_info.last_activation_till_time is 'denotes the time till when the user is salary program active due to last activation entry';
comment on column raise_salary_verification_by_ops_eligibility_info.last_verified_salary_txn_timestamp is 'denotes the last verified salary txn timestamp';
comment on column raise_salary_verification_by_ops_eligibility_info.last_verified_salary_txn_verified_by is '{"proto_type":"salaryprogram.SalaryTxnVerificationRequestVerifiedBy", "comment":"denotes the source by which last salary txn was verified, like OPS, SYSTEM etc"}';

-- useful for fetching salary active users.
create index if not exists raise_salary_ver_by_ops_eligibility_info_activation_till_idx on raise_salary_verification_by_ops_eligibility_info using btree (last_activation_till_time);

-- only one entry should exist for given actor
create unique index if not exists raise_salary_ver_by_ops_eligibility_info_actor_id_unique_idx on raise_salary_verification_by_ops_eligibility_info (actor_id);

-- data team need this index for periodic snapshot query
create index if not exists raise_salary_ver_by_ops_eligibility_info_updated_at_idx ON raise_salary_verification_by_ops_eligibility_info USING btree (updated_at);
