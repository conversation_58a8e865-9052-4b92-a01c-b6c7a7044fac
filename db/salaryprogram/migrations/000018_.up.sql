-- since we can have multiple activation now (salary txn detection/ salary lite mandate execution),
-- so this column will be null in case activation action is not salary txn detection
ALTER TABLE IF EXISTS salary_program_activation_history
	ALTER COLUMN salary_txn_verification_request_id DROP NOT NULL;

-- only one activation entry should exist for a activation_action_ref_id
CREATE UNIQUE INDEX IF NOT EXISTS salary_program_act_history_activation_action_ref_id_unique_idx ON salary_program_activation_history (activation_action_ref_id);

