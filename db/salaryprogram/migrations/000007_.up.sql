-- health_insurance_policy_issuance_requests table
create table if not exists health_insurance_policy_issuance_requests
(
	id 							uuid not null default uuid_generate_v4() primary key,
	actor_id 					varchar not null,
	policy_vendor 				varchar not null,
	vendor_request_id  			varchar not null,
	request_status 				varchar not null,
	request_expires_at         	timestamp with time zone not null,
	created_at         			timestamp with time zone default now() not null,
	updated_at         			timestamp with time zone default now() not null,
	deleted_at         			timestamp with time zone default null
);

comment on table health_insurance_policy_issuance_requests is 'stores the details of request needed for initiating a health insurance policy purchase';
comment on column health_insurance_policy_issuance_requests.actor_id is 'stores the actor for whom request was created';
comment on column health_insurance_policy_issuance_requests.policy_vendor is '{"proto_type":"vendorgateway.Vendor", "comment":"stores the vendor who would be fulfilling the policy request"}';
comment on column health_insurance_policy_issuance_requests.vendor_request_id is 'stores the unique request id passed to vendor while purchasing the policy';
comment on column health_insurance_policy_issuance_requests.request_status is '{"proto_type":"salaryprogram.healthinsurance.PolicyIssuanceRequestStatus", "comment":"stores the status of issuance request"}';

-- only one issuance should exist for a given (vendor, vendor_request_id) combination.
create unique index if not exists health_ins_policy_issuance_req_vendor_vendor_req_id_unique_idx ON health_insurance_policy_issuance_requests (policy_vendor, vendor_request_id);

-- health_insurance_policy_issuance_requests would be frequently queried on actorId and request status combination.
create index if not exists health_ins_policy_issuance_req_actor_id_req_status_idx ON health_insurance_policy_issuance_requests (actor_id, request_status);

-- data team need this index for periodic snapshot query
create index if not exists health_ins_policy_issuance_req_updated_at_idx ON health_insurance_policy_issuance_requests USING btree (updated_at);


-- health_insurance_policy_details table
create table if not exists health_insurance_policy_details
(
	id 								uuid not null default uuid_generate_v4() primary key,
	actor_id 						varchar not null,
	policy_vendor 					varchar not null,
	vendor_policy_id 				varchar not null,
	vendor_parent_policy_id  		varchar default null,
	policy_issuance_request_id 		uuid default null references health_insurance_policy_issuance_requests(id),
	policy_active_from         		timestamp with time zone not null,
	policy_active_till         		timestamp with time zone default null,
	policy_metadata 				jsonb not null,
	policy_request_source			varchar not null,
	created_at         				timestamp with time zone default now() not null,
	updated_at         				timestamp with time zone default now() not null,
	deleted_at         				timestamp with time zone default null
);

comment on table health_insurance_policy_details is 'stores details of the issued health insurance policies';
comment on column health_insurance_policy_details.actor_id is 'stores the actor to whom this policy is issued';
comment on column health_insurance_policy_details.policy_vendor is '{"proto_type":"vendorgateway.Vendor", "comment":"stores the vendor who is fulfilling this policy"}';
comment on column health_insurance_policy_details.vendor_policy_id is 'stores the unique identifier for a policy at vendor’s end';
comment on column health_insurance_policy_details.vendor_parent_policy_id is 'stores the vendor_policy_id of initial policy which got auto-renewed, would be non-empty only for policies which were issued due to existing policy getting auto-renewed at vendor end';
comment on column health_insurance_policy_details.policy_issuance_request_id is 'stores the id of policy_issuance_request which led to issuance of this policy';

create unique index if not exists health_ins_policy_details_vendor_vendor_policy_id_unique_idx ON health_insurance_policy_details (policy_vendor, vendor_policy_id);

create unique index if not exists health_ins_policy_details_policy_issuance_req_id_unique_idx ON health_insurance_policy_details (policy_issuance_request_id);

create index if not exists health_ins_policy_details_actor_id_policy_active_till_from_idx ON health_insurance_policy_details (actor_id, policy_active_till, policy_active_from);

-- data team need this index for periodic snapshot query
create index if not exists health_ins_policy_details_updated_at_idx ON health_insurance_policy_details USING btree (updated_at);
