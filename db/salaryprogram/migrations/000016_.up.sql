ALTER TABLE salary_program_activation_history RENAME activation_source TO activation_action;
ALTER TABLE salary_program_activation_history RENAME activation_source_ref_id TO activation_action_ref_id;

ALTER TABLE salary_lite_mandate_execution_requests ADD COLUMN IF NOT EXISTS execution_failure_reason VARCHAR NOT NULL DEFAULT 'EXECUTION_FAILURE_REASON_UNSPECIFIED';

COMMENT ON COLUMN salary_program_activation_history.activation_action IS '{"proto_type":"salaryprogram.SalaryProgramActivationAction", "comment":"stores the action through which user is salary activated, ex- SALARY_TXN, MANDATE_EXECUTION"}';
COMMENT ON COLUMN salary_program_activation_history.activation_action_ref_id IS 'stores the reference id for salary activation action like- salary txn verification req id, mandate execution request id';
COMMENT ON COLUMN salary_lite_mandate_execution_requests.execution_failure_reason IS '{"proto_type":"salaryprogram.SalaryLiteMandateExecutionFailureReason", "comment":"stores the failure reason if mandate execution is failed"}';
