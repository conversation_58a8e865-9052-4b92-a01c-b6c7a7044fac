-- salary_lite_mandate_requests table
CREATE TABLE IF NOT EXISTS salary_lite_mandate_requests
(
    id                       uuid                                   not null default uuid_generate_v4() primary key,
    actor_id                 varchar                                not null,
    recurring_payment_id     varchar                                not null,
    preferred_execution_date date                                   not null,
    amount                   jsonb                                  not null,
    request_status           varchar                                not null default 'SALARY_LITE_MANDATE_REQUEST_STATUS_UNSPECIFIED',
    created_at               timestamp with time zone default now() not null,
    updated_at               timestamp with time zone default now() not null,
    deleted_at               timestamp with time zone default null
);

COMMENT ON TABLE salary_lite_mandate_requests IS 'stores the details of request for initiating a salary lite mandate';
COMMENT ON COLUMN salary_lite_mandate_requests.actor_id IS 'stores the actor for whom request is created';
COMMENT ON COLUMN salary_lite_mandate_requests.recurring_payment_id IS 'stores the recurring payment id';
COMMENT ON COLUMN salary_lite_mandate_requests.preferred_execution_date IS 'stores the user preferred mandate execution date';
COMMENT ON COLUMN salary_lite_mandate_requests.amount IS '{"proto_type":"types.Money", "comment":"stores the mandate amount"}';
COMMENT ON COLUMN salary_lite_mandate_requests.request_status IS '{"proto_type":"salaryprogram.SalaryLiteMandateRequestStatus", "comment":"stores the status of mandate request"}';

-- only one mandate request should exist for a given recurring_payment_id.
CREATE UNIQUE INDEX IF NOT EXISTS salary_lite_mandate_req_recurring_payment_id_unique_idx ON salary_lite_mandate_requests (recurring_payment_id);

-- for fetching mandate requests for a actor
CREATE INDEX IF NOT EXISTS salary_lite_mandate_req_actor_id_idx ON salary_lite_mandate_requests USING btree (actor_id);

-- for fetching mandates that needs to be executed on a date by any execution job/workflow.
CREATE INDEX IF NOT EXISTS salary_lite_mandate_req_preferred_execution_date_idx ON salary_lite_mandate_requests USING btree (preferred_execution_date);

-- data team need this index for periodic snapshot query
CREATE INDEX IF NOT EXISTS salary_lite_mandate_req_updated_at_idx ON salary_lite_mandate_requests USING btree (updated_at);

ALTER TABLE salary_program_activation_history
    ADD COLUMN activation_source VARCHAR NOT NULL DEFAULT 'SALARY_TXN',
    --  TODO(yuvraj) mark it as not null post back-filling
    ADD COLUMN activation_source_ref_id VARCHAR;

COMMENT ON COLUMN salary_program_activation_history.activation_source IS '{"proto_type":"salaryprogram.SalaryProgramActivationSource", "comment":"stores the source from which user is salary activated, ex- SALARY_TXN, MANDATE_EXECUTION"}';
COMMENT ON COLUMN salary_program_activation_history.activation_source_ref_id IS 'stores the reference id for salary activation source like- salary txn verification req id, mandate execution id';
