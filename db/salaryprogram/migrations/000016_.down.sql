ALTER TABLE salary_program_activation_history RENAME activation_action TO activation_source;
ALTER TABLE salary_program_activation_history RENAME activation_action_ref_id TO activation_source_ref_id;

ALTER TABLE salary_lite_mandate_execution_requests DROP COLUMN IF EXISTS execution_failure_reason;

COMMENT ON COLUMN public.salary_program_activation_history.activation_source IS '{"proto_type":"salaryprogram.SalaryProgramActivationSource", "comment":"stores the source from which user is salary activated, ex- SALARY_TXN, MANDATE_EXECUTION"}';
COMMENT ON COLUMN public.salary_program_activation_history.activation_source_ref_id IS 'stores the reference id for salary activation source like- salary txn verification req id, mandate execution id';
