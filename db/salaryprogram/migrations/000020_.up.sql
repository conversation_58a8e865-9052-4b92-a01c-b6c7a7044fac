CREATE TABLE IF NOT EXISTS whitelisted_b2b_users
(
	id           VARCHAR                                NOT NULL PRIMARY KEY,
	phone_number VARCHAR                                NOT NULL,
	employer_id  VARCHAR,
	created_at   TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated_at   TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	deleted_at   TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

comment on table whitelisted_b2b_users is 'stores the details of users whitelisted for b2b salary program, we perform some chores for whitelisted users like- automatically register employer for salary program for the user, etc';
comment on column whitelisted_b2b_users.phone_number is 'denotes the phone number of the user with country code, ex- 919993231211';
comment on column whitelisted_b2b_users.employer_id is 'denotes the id of the employer of the user while whitelisting';

CREATE UNIQUE INDEX IF NOT EXISTS whitelisted_b2b_users_phone_number_idx ON whitelisted_b2b_users (phone_number);

-- data team need this index for periodic snapshot query
CREATE INDEX IF NOT EXISTS whitelisted_b2b_users_updated_at_idx ON whitelisted_b2b_users USING btree (updated_at);


