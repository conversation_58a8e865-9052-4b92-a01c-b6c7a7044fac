-- needed for queries that fetch activation histories across registrations in a given time-range.
create index if not exists salary_program_activation_history_active_till_from_idx on salary_program_activation_history(active_till, active_from);

-- swapping the order of active_from and active_till in index as most queries have where condition like active_from <= current_timestamp and active_till > current_timestamp and number of entries
-- satisfying active_till > current_timestamp clause should be lesser than those satisfying active_from <= current_timestamp, so this change should lead to relatively lesser iterations during index scan.
create index if not exists salary_program_activation_history_reg_id_active_till_from_idx ON salary_program_activation_history (salary_program_registration_id, active_till, active_from);
drop index if exists salary_program_activation_history_reg_id_active_from_till_idx;
