ALTER TABLE IF EXISTS salary_txn_verification_requests
	ADD COLUMN IF NOT EXISTS verification_failure_reason_category VARCHAR NOT NULL DEFAULT 'FAILURE_REASON_CATEGORY_UNSPECIFIED',
	ADD COLUMN IF NOT EXISTS verification_failure_reason_sub_category VARCHAR NOT NULL DEFAULT 'FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED',
	ADD COLUMN IF NOT EXISTS verification_remark VARCHAR;

COMMENT ON COLUMN salary_txn_verification_requests.verification_failure_reason_category IS '{"proto": "salaryprogram.SalaryTxnVerificationFailureReasonCategory", "comment": "stores the verification failure reason category if the salary txn verification is failed"}';
COMMENT ON COLUMN salary_txn_verification_requests.verification_failure_reason_sub_category IS '{"proto": "salaryprogram.SalaryTxnVerificationFailureReasonSubCategory", "comment": "stores the verification failure reason sub-category if the salary txn verification is failed"}';
COMMENT ON COLUMN salary_txn_verification_requests.verification_remark IS 'stores the salary txn verification remark';
