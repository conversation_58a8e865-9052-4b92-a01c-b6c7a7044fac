-- registrations table
create table if not exists salary_program_referrals_seasons
(
	id 							uuid not null  default uuid_generate_v4() primary key,

	display_meta 				jsonb not null,
    -- milestones is non-mandatory field, so not applying not null constraint on the column.
	milestones 					jsonb,
	display_since  				timestamp with time zone not null,
	display_till  				timestamp with time zone not null,
	active_since  				timestamp with time zone not null,
	active_till  				timestamp with time zone not null,

	created_at         			timestamp with time zone default now() not null,
	updated_at         			timestamp with time zone default now() not null,
	deleted_at         			timestamp with time zone default null
);

comment on table salary_program_referrals_seasons is 'stores the details of salaryprogram referrals seasons';
comment on column salary_program_referrals_seasons.display_meta is '{"proto_type":"salaryprogram.referrals.SeasonDisplayMeta", "comment":"stores the details of salaryprogram referrals seasons"}';
comment on column salary_program_referrals_seasons.milestones is '{"proto_type":"salaryprogram.referrals.SeasonMilestones", "comment":"stores the milestones related details of a season"}';
comment on column salary_program_referrals_seasons.display_since is 'denotes the time from when the season should be displayed on the app';
comment on column salary_program_referrals_seasons.display_till is 'denotes the time till when the season should be displayed on the app';
comment on column salary_program_referrals_seasons.active_since is 'denotes the time from when the season is active';
comment on column salary_program_referrals_seasons.active_till is 'denotes the time till when the season is active';

-- seasons are be frequently queried on active_since,active_till filter.
create index if not exists salary_program_referrals_seasons_active_till_since_idx ON salary_program_referrals_seasons (active_till, active_since);

-- data team need this index for periodic snapshot query
create index if not exists salary_program_referrals_seasons_updated_at_idx ON salary_program_referrals_seasons USING btree (updated_at);
