-- salary_program_registrations sql
insert into salary_program_registrations(id, actor_id, account_id, registration_flow_version, completed_at,account_type,registration_flow_type) values
('registration-id-1', 'actor-1', 'account-id-1', 'REGISTRATION_FLOW_VERSION_V1', '2023-06-20 10:58:29.000 +00:00','SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_FI_FED_SAVINGS_ACC','SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE'),
('registration-id-2', 'actor-2', 'account-id-2', 'REGIS<PERSON>ATION_FLOW_VERSION_V1', null,'SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_FI_FED_SAVINGS_ACC','SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE'),
('registration-id-3', 'actor-3', 'account-id-3', 'REGISTRATION_FLOW_VERSION_V1', null,'SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_FI_FED_SAVINGS_ACC','SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE'),
('registration-id-4', 'aa-actor-id-1', 'aa-account-id-1', 'REGISTRATION_FLOW_VERSION_V1', null,'SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_AA_SAVINGS_ACC','SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_AA_SALARY');

-- salary_program_registration_stage_details sql
insert into salary_program_registration_stage_details(id, salary_program_registration_id, stage_name, stage_status) values
('43284dbf-3deb-4bdd-a8b8-3eb13d527e09', 'registration-id-1', 'REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION', 'REGISTRATION_STAGE_STATUS_COMPLETED'),
('43284dbf-3deb-4bdd-a8b8-3eb13d527e01', 'registration-id-1', 'REGISTRATION_STAGE_FULL_KYC_COMPLETION', 'REGISTRATION_STAGE_STATUS_INITIATED'),
('43284dbf-3deb-4bdd-a8b8-3eb13d527e03', 'registration-id-3', 'REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION', 'REGISTRATION_STAGE_STATUS_COMPLETED'),
('43284dbf-3deb-4bdd-a8b8-3eb13d527e04', 'registration-id-3', 'REGISTRATION_STAGE_FULL_KYC_COMPLETION', 'REGISTRATION_STAGE_STATUS_COMPLETED');

-- salary_txn_verification_requests sql
insert into salary_txn_verification_requests(id, actor_id, txn_id, request_source, verification_status, verification_sub_status, verified_by, salary_txn_verification_version, txn_employer_id, txn_timestamp, created_at, auto_verifier_meta) values
('43284dbf-3deb-4bdd-a8b8-3eb13d527e11', 'actor-3', 'txn-id-1', 'VERIFICATION_REQUEST_SOURCE_TXN_EVENT', 'REQUEST_STATUS_VERIFIED', 'SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED',  'VERIFIED_BY_SYSTEM', 'VERIFICATION_VERSION_V1', 'emp-id-1', '2022-06-20 10:58:29.000 +00:00', '2022-06-20 10:58:29.000 +00:00', '{"verification_success_meta": {"field_used_for_txn_remitter_name": "PAYMENT_INSTRUMENT_VERIFIED_NAME", "employer_matched_with_txn_remitter": "USER_DECLARED_EMPLOYER", "remitter_to_employer_match_logic": "DS_NAME_MATCH"}}'),
('43284dbf-3deb-4bdd-a8b8-3eb13d527e12', 'actor-3', 'txn-id-2', 'VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST', 'REQUEST_STATUS_IN_PROGRESS', 'SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED', 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED', 'VERIFICATION_VERSION_V1', 'emp-id-1', '2022-06-20 10:58:29.000 +00:00', '2022-06-21 10:58:29.000 +00:00', null),
('43284dbf-3deb-4bdd-a8b8-3eb13d527e13', 'actor-3', 'txn-id-3', 'VERIFICATION_REQUEST_SOURCE_TXN_EVENT', 'REQUEST_STATUS_VERIFIED', 'SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED',  'VERIFIED_BY_SYSTEM', 'VERIFICATION_VERSION_V1', 'emp-id-1', '2022-07-20 10:58:29.000 +00:00', '2022-07-20 10:58:29.000 +00:00', null);

-- salary_program_activation_history sql
insert into salary_program_activation_history(id, salary_program_registration_id, active_from, active_till, salary_txn_verification_request_id, activation_action, activation_action_ref_id, created_at) values
('43284dbf-3deb-4bdd-a8b8-3eb13d527e21', 'registration-id-3', '2022-06-20 10:58:29.000 +00:00', '2023-06-20 10:58:29.000 +00:00', '43284dbf-3deb-4bdd-a8b8-3eb13d527e11','SALARY_TXN', '43284dbf-3deb-4bdd-a8b8-3eb13d527e11', '2022-07-13 07:25:05.565277 +00:00'),
('43284dbf-3deb-4bdd-a8b8-3eb13d527e22', 'registration-id-3', '2023-07-20 10:58:29.000 +00:00', '2025-06-20 10:58:29.000 +00:00', '43284dbf-3deb-4bdd-a8b8-3eb13d527e13','MANDATE_EXECUTION', '8a6917c8-8366-432a-8c78-248dd1a4ab47', '2023-08-15 07:25:05.565277 +00:00');

-- salary_program_referrals_seasons sql
INSERT INTO public.salary_program_referrals_seasons (id, display_meta, milestones, display_since, display_till, active_since, active_till, created_at, updated_at, deleted_at) values
('854ed7a9-8895-41df-ad51-5ad5b9c80376', '{"cta": {"name": "Invite Colleagues", "deeplink": {"screen": "MY_REWARDS_SCREEN"}}, "headerTitle": "header-title-1", "headerIconUrl": "header-icon-1", "importantInfo": ["important info 1", "important info 2"], "howItWorksInfo": {"infos": [{"desc": "desc-1", "iconUrl": "icon-url-1"}]}}', '{"milestones": [{"display": {"desc": "20% extra Fi-Coins for you and your colleagues", "title": "5 Colleagues"}}]}', '2022-09-12 14:21:00.000000 +00:00', '2022-09-12 14:31:00.000000 +00:00', '2022-09-12 14:21:00.000000 +00:00', '2022-09-12 14:31:00.000000 +00:00', '2022-09-12 14:31:14.555432 +00:00', '2022-09-12 14:31:14.555432 +00:00', null);

-- health_insurance_policy_issuance_requests sql
INSERT INTO public.health_insurance_policy_issuance_requests (id, actor_id, policy_vendor, vendor_request_id, request_status, request_expires_at, created_at, updated_at, deleted_at) VALUES
('12fed212-8167-4b64-a212-6fb438582ace', 'actor-1', 'RISKCOVRY', 'vendor-req-id-1', 'REQUEST_STATUS_CREATED', '2022-12-20 10:58:29.000000 +00:00', '2022-12-13 07:25:05.565277 +00:00', '2022-12-13 07:25:05.565277 +00:00', null),
('12fed212-8167-4b64-a212-6fb438582ac1', 'actor-1', 'RISKCOVRY', 'vendor-req-id-2', 'REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL', '2022-12-20 10:58:29.000000 +00:00', '2022-12-15 07:25:05.565277 +00:00', '2022-12-15 07:25:05.565277 +00:00', null);

-- health_insurance_policy_details sql
INSERT INTO public.health_insurance_policy_details (id, actor_id, policy_vendor, vendor_policy_id, vendor_parent_policy_id, policy_issuance_request_id, policy_active_from, policy_active_till, policy_metadata, policy_request_source, created_at, updated_at, deleted_at) VALUES
('2ab63d1c-f1f1-4523-a9c9-89e57c68a21b', 'actor-1', 'RISKCOVRY', 'vendor-policy-id-1', null, '12fed212-8167-4b64-a212-6fb438582ac1', '2022-12-20 10:58:29.000000 +00:00', null, '{"coi": "coi-1", "premium": {"currencyCode" : "INR", "units" : 10}, "sumAssured": {"currencyCode" : "INR", "units" : 1000}, "insuredPeople": [{"name": "name-1", "email": "email-1"}], "vendorPolicyId": "vendor-policy-id-1", "policyActiveFrom": "2022-12-20T10:58:29Z", "certificateNumber": "certification-number-1"}', 'REQUEST_SOURCE_FI_APP', '2022-12-13 07:43:18.659299 +00:00', '2022-12-13 07:43:18.659299 +00:00', null);

INSERT INTO raise_salary_verification_by_ops_eligibility_info (id, actor_id, eligibility_status, last_activation_from_time, last_activation_till_time, last_verified_salary_txn_timestamp, last_verified_salary_txn_verified_by, registration_completion_time) VALUES
('12fed212-8167-4b64-a212-6fb438582ace', 'actor-1', 'NEW', '2022-12-20 10:58:29.000000 +00:00', '2024-12-20 10:58:29.000000 +00:00', '2023-02-20 10:58:29.000000 +00:00', 'VERIFIED_BY_OPS', '2022-11-20 10:58:29.000000 +00:00'),
('22fed212-8167-4b64-a212-6fb438582ace', 'actor-2','PENDING', '2022-12-20 10:58:29.000000 +00:00', '2025-12-20 10:58:29.000000 +00:00', '2023-01-20 10:58:29.000000 +00:00', 'VERIFIED_BY_SYSTEM', '2022-11-20 10:58:29.000000 +00:00'),
('9b0d0bcb-cb01-4672-aca9-a42378fb16dd', 'actor-3','NEW', null, null, null, 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED', '2023-7-20 10:58:29.000000 +00:00'),
('fd8f7b95-e1db-40d9-ac9b-526b0c36bfe5', 'actor-4','NEW', null, null, null, 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED', '2023-7-21 10:58:29.000000 +00:00');

INSERT INTO salary_lite_mandate_requests (id, actor_id, recurring_payment_id, preferred_execution_day_of_month, bank, amount, request_status, created_at, updated_at, deleted_at) VALUES
('77bc309a-2789-4d3d-add1-c91fefd69df7', 'actor-1', 'ef2af65d-18a4-4013-818d-8297d72aae01', 20, 'HDFC', '{"currency_code" : "INR", "units" : 100000}', 'IN_PROGRESS', '2023-7-21 00:00:00.000000 +00:00', '2023-7-21 00:00:00.000000 +00:00', null),
('27f22e78-0512-4009-9620-ae6f5a4e0fc2', 'actor-2', 'd829c6b3-7934-45a1-81c6-5ad84b0e99fc', 25, 'HDFC', '{"currency_code" : "INR", "units" : 500000}', 'SUCCESS', '2023-7-22 00:00:00.000000 +00:00', '2023-7-22 00:00:00.000000 +00:00', null);

INSERT INTO salary_lite_mandate_execution_requests (id, recurring_payment_id, request_status, actor_id, mandate_request_id, created_at, updated_at, deleted_at, provenance) VALUES
('cfbd44a9-4812-496f-9132-5586ed846c99', 'f4a26c4b-3c25-4afe-b67d-09a00b9cc5ca', 'EXECUTION_IN_PROGRESS', 'actor-1', '77bc309a-2789-4d3d-add1-c91fefd69df7', '2023-08-07 00:00:00.000000 +00:00', '2023-08-07 00:00:00.000000 +00:00', null, 'PROVENANCE_RETRY_EXECUTION_FROM_APP'),
('295d46fe-4ff5-45bb-a464-b2e30d4fb6f2', 'f4a26c4b-3c25-4afe-b67d-09a00b9cc5ca', 'EXECUTION_SUCCESS', 'actor-2', '27f22e78-0512-4009-9620-ae6f5a4e0fc2', '2023-08-08 00:00:00.000000 +00:00', '2023-08-08 00:00:00.000000 +00:00', null, 'PROVENANCE_SCHEDULED_JOB');

INSERT INTO whitelisted_b2b_users (id, phone_number, employer_id, hrms_management_vendor, vendor_employer_id, vendor_user_id, created_at, updated_at) VALUES
('27f22e78-0512-4009-9620-ae6f5a4e0fc2', '************', 'd829c6b3-7934-45a1-81c6-5ad84b0e99fc', 'TARTAN', 'vendor-employer-id-1', 'vendor-user-id-1', '2023-7-22 00:00:00.000000 +00:00', '2023-7-22 00:00:00.000000 +00:00');

INSERT INTO salary_estimations(id,actor_id, last_estimated_amount, salary_account_source_type, status) VALUES ('700ff2a1-895e-4f55-ac47-94be94bda624','aa-actor-id-1', '{"units": 50000, "currency_code": "INR"}','SALARY_ACCOUNT_SOURCE_TYPE_AA_ACCOUNT','SALARY_ESTIMATION_STALENESS_STATUS_TYPE_LATEST');

INSERT INTO aa_salary_criteria (id, criteria_name, details, created_at, updated_at) VALUES
    ('27f22e78-0512-4009-9620-ae6f5a4e0faa', 'CRITERIA_NAME_C_ONE', '{"criteriaDetailList":[{"salaryBand":"SALARY_BAND_1","actions":[{"actionDetails":{"salary":{"minSalary":{"currencyCode":"INR","units":"20000"},"maxSalary":{"currencyCode":"INR","units":"30000"},"minRatio":0.6,"maxRatio":0.8}}}]},{"salaryBand":"SALARY_BAND_2","actions":[{"actionDetails":{"salary":{"minSalary":{"currencyCode":"INR","units":"20000"},"maxSalary":{"currencyCode":"INR","units":"30000"},"minRatio":0.8,"maxRatio":1.0}}}]},{"salaryBand":"SALARY_BAND_1","actions":[{"actionDetails":{"salary":{"minSalary":{"currencyCode":"INR","units":"30000"},"maxSalary":{"currencyCode":"INR","units":"40000"},"minRatio":0.6,"maxRatio":0.8}}}]},{"salaryBand":"SALARY_BAND_2","actions":[{"actionDetails":{"salary":{"minSalary":{"currencyCode":"INR","units":"30000"},"maxSalary":{"currencyCode":"INR","units":"40000"},"minRatio":0.8,"maxRatio":1}}}]}]}', '2023-7-22 00:00:00.000000 +00:00', '2023-7-22 00:00:00.000000 +00:00');

INSERT INTO aa_salary_txn_verification_requests (id, salary_program_registrations_ref_id, actor_id, salary_band, verified_by, request_source, verification_status, salary_amount_committed, salary_estimations_ref_id, aa_salary_criteria_ref_id, created_at) VALUES
    ('55f22e78-0512-4009-9620-ae6f5a4e0faa', 'registration-id-4', 'aa-actor-id-1', 'SALARY_BAND_1', 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED', 'VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST', 'REQUEST_STATUS_IN_PROGRESS', '{"units": 35000, "currency_code": "INR"}', '700ff2a1-895e-4f55-ac47-94be94bda624', '27f22e78-0512-4009-9620-ae6f5a4e0faa', '2023-7-22 00:00:00.000000 +00:00'),
    ('65f22e78-0512-4009-9620-ae6f5a4e0faa', 'registration-id-4', 'aa-actor-id-1', 'SALARY_BAND_1', 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED', 'VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST', 'REQUEST_STATUS_COMPLETED', '{"units": 35000, "currency_code": "INR"}', '700ff2a1-895e-4f55-ac47-94be94bda624', '27f22e78-0512-4009-9620-ae6f5a4e0faa', '2023-7-23 00:00:00.000000 +00:00'),
    ('75f22e78-0512-4009-9620-ae6f5a4e0faa', 'registration-id-4', 'aa-actor-id-1', 'SALARY_BAND_1', 'SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED', 'VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST', 'REQUEST_STATUS_COMPLETED', '{"units": 35000, "currency_code": "INR"}', '700ff2a1-895e-4f55-ac47-94be94bda624', '27f22e78-0512-4009-9620-ae6f5a4e0faa', '2023-7-24 00:00:00.000000 +00:00');
