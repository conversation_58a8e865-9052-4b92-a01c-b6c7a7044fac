insert into collection_leads (id, actor_id, account_id, vendor, vendor_id, state, created_at) values ('30fdffbc-4297-11ee-be56-0242ac120002', 'actor-1', 'account-1', 'CREDGENICS', 'external-loan-1', 'LEAD_STATE_ACTIVE', '2023-08-22 00:00:00.000000');
insert into collection_leads (id, actor_id, account_id, vendor, vendor_id, state, created_at) values ('6197dcec-4297-11ee-be56-0242ac120002', 'actor-1', 'account-2', 'CREDGENICS', 'external-loan-2', 'LEAD_STATE_ACTIVE', '2023-08-23 00:00:00.000000');
insert into collection_leads (id, actor_id, account_id, vendor, vendor_id, state, created_at) values ('6a96342e-4297-11ee-be56-0242ac120002', 'actor-1', 'account-3', 'CREDGENICS', 'external-loan-3', 'LEAD_STATE_ACTIVE', '2023-08-24 00:00:00.000000');
insert into collection_leads (id, actor_id, account_id, vendor, vendor_id, state, created_at) values ('71a482f2-4297-11ee-be56-0242ac120002', 'actor-1', 'account-4', 'CREDGENICS', 'external-loan-4', 'LEAD_STATE_ACTIVE', '2023-08-25 00:00:00.000000');
insert into collection_leads (id, actor_id, account_id, vendor, vendor_id, state, created_at) values ('********-4297-11ee-be56-0242ac120002', 'actor-1', 'account-5', 'CREDGENICS', 'external-loan-5', 'LEAD_STATE_ACTIVE', '2023-08-26 00:00:00.000000');
insert into collection_leads (id, actor_id, account_id, vendor, vendor_id, state, created_at) values ('7ffe3cc6-4297-11ee-be56-0242ac120002', 'actor-1', 'account-6', 'CREDGENICS', 'external-loan-6', 'LEAD_STATE_ACTIVE', '2023-08-27 00:00:00.000000');
insert into collection_leads (id, actor_id, account_id, vendor, vendor_id, state, created_at) values ('8ffe3cc6-4297-11ee-be56-0242ac120002', 'actor-1', 'account-7', 'CREDGENICS', 'external-loan-7', 'LEAD_STATE_ACTIVE', '2023-08-27 00:00:00.000000');

insert into collection_allocations (id, lead_id, date_of_allocation, default_date, vendor_status, recovery_status, created_at, updated_at) values ('72dc2560-9153-47f9-b03c-99f41cc8b629' , '30fdffbc-4297-11ee-be56-0242ac120002', '2023-08-18', '2023-08-22', 'VENDOR_STATUS_PENDING', 'RECOVERY_STATUS_NOT_RECOVERED', '2023-08-22 00:00:00.000000', '2023-08-22 00:00:00.000000');
insert into collection_allocations (id, lead_id, date_of_allocation, default_date, vendor_status, recovery_status, created_at, updated_at) values ('8795f56e-4297-11ee-be56-0242ac120002' , '6197dcec-4297-11ee-be56-0242ac120002', '2023-08-18', '2023-08-23', 'VENDOR_STATUS_PENDING', 'RECOVERY_STATUS_NOT_RECOVERED', '2023-08-23 00:00:00.000000', '2023-08-23 00:00:00.000000');
insert into collection_allocations (id, lead_id, date_of_allocation, default_date, vendor_status, recovery_status, created_at, updated_at) values ('8c1b21fe-4297-11ee-be56-0242ac120002' , '6a96342e-4297-11ee-be56-0242ac120002', '2023-08-18', '2023-08-24', 'VENDOR_STATUS_PENDING', 'RECOVERY_STATUS_NOT_RECOVERED', '2023-08-24 00:00:00.000000', '2023-08-24 00:00:00.000000');
insert into collection_allocations (id, lead_id, date_of_allocation, default_date, vendor_status, recovery_status, created_at, updated_at) values ('902f6ad4-4297-11ee-be56-0242ac120002' , '71a482f2-4297-11ee-be56-0242ac120002', '2023-08-18', '2023-08-25', 'VENDOR_STATUS_PENDING', 'RECOVERY_STATUS_NOT_RECOVERED', '2023-08-25 00:00:00.000000', '2023-08-25 00:00:00.000000');
insert into collection_allocations (id, lead_id, date_of_allocation, default_date, vendor_status, recovery_status, created_at, updated_at) values ('93fd65b2-4297-11ee-be56-0242ac120002' , '********-4297-11ee-be56-0242ac120002', '2023-08-18', '2023-08-26', 'VENDOR_STATUS_PENDING', 'RECOVERY_STATUS_NOT_RECOVERED', '2023-08-26 00:00:00.000000', '2023-08-26 00:00:00.000000');
insert into collection_allocations (id, lead_id, date_of_allocation, default_date, vendor_status, recovery_status, created_at, updated_at) values ('981ff1fa-4297-11ee-be56-0242ac120002' , '7ffe3cc6-4297-11ee-be56-0242ac120002', '2023-08-18', '2023-08-27', 'VENDOR_STATUS_PENDING', 'RECOVERY_STATUS_NOT_RECOVERED', '2023-08-27 00:00:00.000000', '2023-08-27 00:00:00.000000');
insert into collection_allocations (id, lead_id, date_of_allocation, default_date, vendor_status, recovery_status, created_at, updated_at) values ('991ff1fa-4297-11ee-be56-0242ac120002' , '8ffe3cc6-4297-11ee-be56-0242ac120002', '2023-08-18', '2023-08-28', 'VENDOR_STATUS_PENDING', 'RECOVERY_STATUS_NOT_RECOVERED', '2023-08-28 00:00:00.000000', '2023-08-28 00:00:00.000000');

-- loan_requests table
INSERT INTO loan_requests (id,actor_id,offer_id, orch_id, loan_program, loan_account_id, vendor_request_id, vendor, type, status, sub_status,details,created_at,updated_at,deleted_at, client_request_id) VALUES
	('request-id-2','act-2','off-2','orch-2', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','*********','req-2','FEDERAL','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CREATED','LOAN_REQUEST_SUB_STATUS_CREATED','{}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL, 'cr-id-1'),
	('request-id-6','act-6','off-6','orch-6', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','acc-6','req-6','FEDERAL','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CREATED','LOAN_REQUEST_SUB_STATUS_CREATED','{"loanInfo": {"amount": {"currencyCode": "INR", "units": "100000"}, "deductions": {"advanceInterest": {"currencyCode": "INR", "units": "500"}, "gst": {"currencyCode": "INR", "units": "200"}, "processingFee": {"currencyCode": "INR", "units": "300"}, "totalDeductions": {"currencyCode": "INR", "units": "1000"}}, "disbursalAmount": {"currencyCode": "INR", "units": "99000"}, "emiAmount": {"currencyCode": "INR", "units": "8000"}, "interestRate": 14.5, "tenureInMonths": 12, "totalPayable": {"currencyCode": "INR", "units": "101000"}}, "otpInfo": {"attemptsCount": 0, "maxAttempts": 3, "otp": "123456"}, "phoneNumber": {"countryCode": 91, "nationalNumber": "**********"}}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL, 'cr-id-2'),
	('request-id-10','act-10','off-10','orch-10','LOAN_PROGRAM_PRE_APPROVED_LOAN','acc-10','req-10','FEDERAL','LOAN_REQUEST_TYPE_CREATION','LOAN_REQUEST_STATUS_CREATED','LOAN_REQUEST_SUB_STATUS_CREATED','{}','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL, 'cr-id-3') ON CONFLICT (id) DO NOTHING;

INSERT INTO loan_accounts (id,actor_id,loan_account_id, loan_type, ifsc_code, loan_amount, disbursed_amount, outstanding_amount, total_payable_amount, loan_end_date, maturity_date, vendor, details, status, created_at,updated_at,deleted_at,loan_program) VALUES
	('account-id-1','act-1','acc-1','LOAN_TYPE_PERSONAL','ifsc-2','{"currency_code": "INR", "units": 5000}','{"currency_code": "INR", "units": 4000}','{"currency_code": "INR", "units": 3000}','{"currency_code": "INR", "units": 3000}','2022-08-15','2022-08-10','FEDERAL','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
	('account-id-2','act-2','acc-2','LOAN_TYPE_PERSONAL','ifsc-2','{"currency_code": "INR", "units": 5000}','{"currency_code": "INR", "units": 4000}','{"currency_code": "INR", "units": 3000}','{"currency_code": "INR", "units": 3000}','2022-08-15','2022-08-10','FEDERAL','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
	('account-id-4','act-4','acc-4','LOAN_TYPE_EARLY_SALARY','ifsc-4','{"currency_code": "INR", "units": 5000}','{"currency_code": "INR", "units": 4000}','{"currency_code": "INR", "units": 3000}','{"currency_code": "INR", "units": 3000}','2022-08-15','2022-08-10','FEDERAL','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
	('account-id-fed-5','act-5','acc-5','LOAN_TYPE_PERSONAL','ifsc','{"currency_code": "INR", "units": 5000}','{"currency_code": "INR", "units": 4000}','{"currency_code": "INR", "units": 3000}','{"currency_code": "INR", "units": 3000}','2022-08-15','2022-08-10','LIQUILOANS','{}','LOAN_ACCOUNT_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL,'LOAN_PROGRAM_PRE_APPROVED_LOAN') ON CONFLICT (id) DO NOTHING;

INSERT INTO loan_installment_info (id, account_id, total_amount, start_date, end_date, total_installment_count, next_installment_date, details, status, deactivated_at, created_at, updated_at, deleted_at) VALUES
	('inst-info-id-2', 'acc-2', '{"currency_code": "INR", "units": 50000}', '2022-08-15', '2022-10-15', 10, '2022-09-15', '{}', 'LOAN_INSTALLMENT_INFO_STATUS_ACTIVE', NULL, '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL) ON CONFLICT (id) DO NOTHING;

INSERT INTO loan_step_executions (id, actor_id, ref_id, orch_id, flow, step_name, details, status, sub_status, staled_at, completed_at, created_at, updated_at, deleted_at) VALUES
	('step-exec-id-2', 'act-2', 'ref-2', 'orch-2', 'LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION', 'LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED', '{}', 'LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED', 'LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED',NULL, NULL, '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL),
	('step-exec-id-6', 'act-6', 'ref-6', 'orch-6', 'LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION', 'LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK', '{}', 'LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED', 'LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED',NULL, '2022-04-01 11:48:50.662941+05:30', '2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL) ON CONFLICT (id) DO NOTHING;

INSERT INTO loan_activities (id, loan_account_id, details, type, created_at, updated_at, deleted_at, reference_id) VALUES
	('act-id-2', 'acc-2', '{}', 'LOAN_ACTIVITY_TYPE_EMI','2022-04-01 06:18:50.662941+00','2022-04-01 11:48:50.662941+05:30',NULL, 'ref-id-1'),
	('act-id-3', 'acc-2', '{}', 'LOAN_ACTIVITY_TYPE_EMI','2022-05-01 06:18:50.662941+00','2022-05-01 11:48:50.662941+05:30',NULL, 'ref-id-3') ON CONFLICT (id) DO NOTHING;

INSERT INTO loan_payment_requests (id, actor_id, account_id, amount, type, orch_id, details, status, sub_status, created_at, updated_at, deleted_at, parent_id) VALUES
	('id-2', 'act-2', 'acc-2', '{"currency_code": "INR", "units": 50000}', 'LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM', 'orch-2', '{}', 'LOAN_PAYMENT_REQUEST_STATUS_CREATED', 'LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED','2022-04-01 11:48:50.662941+05:30','2022-04-01 11:48:50.662941+05:30',NULL, 'parent-id-1') ON CONFLICT (id) DO NOTHING;

-- loan_offers table
INSERT INTO loan_offers (id, actor_id, vendor_offer_id, loan_program, vendor, offer_constraints, processing_info, valid_since, valid_till, deactivated_at, created_at, updated_at, loan_offer_eligibility_criteria_id) VALUES
('loan-offer-id-1', 'actor-1', 'vendor-offer-id-1', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','FEDERAL', '{}', '{}', '2022-07-25 00:00:00.000000 +05:30', '2028-07-25 00:00:00.000000 +05:30', NULL, '2022-07-25 00:00:00.000000 +05:30', '2022-07-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-1'),
('loan-offer-id-2', 'actor-1', 'vendor-offer-id-3', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','FEDERAL', '{}', '{}', '2022-08-25 00:00:00.000000 +05:30', '2028-08-25 00:00:00.000000 +05:30', '2023-08-26 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-1'),
('loan-offer-id-3', 'actor-3', 'vendor-offer-id-4', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','FEDERAL', '{}', '{}', '2022-08-25 00:00:00.000000 +05:30', '2023-03-25 00:00:00.000000 +05:30', NULL, '2022-08-25 00:00:00.000000 +05:30', '2022-08-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-1'),
('loan-offer-id-4', 'actor-4', 'vendor-offer-id-5', 'LOAN_PROGRAM_PRE_APPROVED_LOAN','FEDERAL', '{"maxEmiAmount": {"currencyCode": "INR", "units": "6000"}, "maxLoanAmount": {"currencyCode": "INR", "units": "100000"}, "maxTenureMonths": 36, "minLoanAmount": {"currencyCode": "INR", "units": "10000"}}', '{}', '2022-07-25 00:00:00.000000 +05:30', '2028-07-25 00:00:00.000000 +05:30', NULL, '2022-07-25 00:00:00.000000 +05:30', '2022-07-25 00:00:00.000000 +05:30', 'loan-offer-eligibility-criteria-id-2') ON CONFLICT (id) DO NOTHING;

-- loan_offers table
INSERT INTO loan_offer_eligibility_criteria (id, actor_id, vendor, status, sub_status, loan_scheme, vendor_response, created_at, updated_at, batch_id, loan_program) VALUES
	('loan-offer-eligibility-id-1', 'actor-1', 'FEDERAL', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED', '', '{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30', 'BI-09-03-2023', 'LOAN_PROGRAM_PRE_APPROVED_LOAN'),
	('loan-offer-eligibility-id-2', 'actor-2', 'FEDERAL', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED', '', '{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30', 'BI-09-03-2023', 'LOAN_PROGRAM_PRE_APPROVED_LOAN') ON CONFLICT (id) DO NOTHING;

-- loan_installment_payout table
INSERT INTO loan_installment_payout (id, loan_installment_info_id, amount, due_date, payout_date, details, status, loan_account_id) VALUES
    ('loan-installment-payout-id-1', 'inst-info-id-2', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', 'loan-account-id-1') ON CONFLICT (id) DO NOTHING;

INSERT INTO loan_installment_payout (id, loan_installment_info_id, amount, due_date, payout_date, details, status, vendor_installment_id, due_amount, loan_account_id) VALUES
    ('loan-installment-payout-id-3', 'loan-installment-info-id-common', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', '1', '{"currency_code": "INR", "units": 100000}', 'loan-account-id-3'),
	('loan-installment-payout-id-4', 'loan-installment-info-id-common', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', '2', '{"currency_code": "INR", "units": 100000}', 'loan-account-id-4'),
	('loan-installment-payout-id-5', 'loan-installment-info-id-common', '{"currency_code": "INR", "units": 50000}', '2022-09-01', '2022-09-10', '{}', 'LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS', '3', '{"currency_code": "INR", "units": 100000}', 'loan-account-id-5') ON CONFLICT (id) DO NOTHING;


-- loan_applicants table
INSERT INTO loan_applicants (id, actor_id, vendor, vendor_applicant_id, vendor_request_id, loan_program, status, sub_status, created_at, updated_at, deleted_at) VALUES ('loan-applicant-id-1', 'actor-id-1', 'LIQUILOANS', 'vendor-applicant-id-1', 'vendor-request-id-1', 'LOAN_PROGRAM_PRE_APPROVED_LOAN', 'LOAN_APPLICANT_STATUS_APPROVED', 'LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR', '2023-02-14 10:52:06.373584 +00:00', '2023-02-14 10:52:06.373584 +00:00', NULL) ON CONFLICT (id) DO NOTHING;

INSERT INTO fetched_assets (id, actor_id, vendor, asset_type, vendor_asset_id, user_asset_identifier) VALUES
 ('7c812554-6218-11ee-8c99-0242ac120002', 'actor-id-1', 'FEDERAL', 'ASSET_TYPE_MUTUAL_FUNDS', 'asset-id-1', 'PAN-1');

